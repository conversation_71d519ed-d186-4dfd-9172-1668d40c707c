import React from 'react';
import { EntityFormProps } from '../propTypes';
import { Form } from '@ant-design/compatible';

// import '@ant-design/compatible/assets/index.css';


import { ENTITY_WINDOW_MODULES, ENTITY_WINDOW_OPERATIONS, OVERLAPPING_BOOKING_CAROUSEL_ELEMENT_TYPE } from '../../constants/entityWindowConsts';
import { ENTITY_WINDOW_SECTION_TYPES } from '../../constants/entityWindowConsts';
import { SkillsList } from '../skillsComponents/skillsList';
import { EditSkillsWindowBody } from '../../lib/skillsComponents/editSkillsWindow/body';
import './styles.css';
import { formatFieldValue, isFieldAccessReadonly, isFieldEditable } from '../../utils/fieldControlUtils';
import { isEditAllEntity } from '../../utils/entityStructureUtils';
import { FIELD_DATA_TYPES } from '../../constants';
import { EntityFormSection } from './sections/entityFormSection';
import { ConnectedRequirementsSection } from '../../connectedComponents/connectedEntityWindow/criteria/connectedRequirementsSection';
import { ConnectedRequirementsEntityFormSection } from '../../connectedComponents/connectedEntityWindow/form/connectedRequirementsEntityFormSection';
import { ConnectedRolegroupDpRolesList } from '../../connectedComponents/connectedRoleGroupListPageDetailsPane/connectedRolegroupDpRolesList';
import { ConnectedJobsDpRolegroupList } from '../../connectedComponents/connectedJobsPageDpRolegroupList';
import { getDisplayShortDateFormat } from '../../utils/dateUtils';
import { DISPLAY_DATE_TIME_FORMATS, FEATURE_FLAGS } from '../../constants/globalConsts';
import { ConnectedCriteriaRoleBudgetSection } from '../../connectedComponents/connectedCriteriaRoleBudget/connectedCriteriaRoleBudgetSection';
import ConnectedWorkHistoryDataGrid from '../../connectedComponents/connectedWorkHistoryDataGrid';
import { entityWindowWorkHistoryPagedDataSelector, getWorkHistoryPageDataForEntityWindow } from '../../selectors/workHistorySelector';
import { ConnectedWorkHistroyEntityFormSection } from '../../connectedComponents/connectedEntityWindow/form/connectedWorkHistoryEntityFormSection';
import { EntityFormTabSection } from './sections/entityFormTabSection';
import { Row } from 'antd';
import { ConnectedCMeSection } from '../../connectedComponents/connectedCMe/connectedCMeSection';
import store from '../../store/configureStore';
import { getHiddenSkillFieldIdSelector } from '../../selectors/skillStructureSelectors';
const {
    SKILLS_LIST_SECTION_TYPE,
    COMMENTS_SECTION_TYPE,
    ATTACHMENTS_SECTION_TYPE,
    ROLE_GROUP_LIST_SECTION_TYPE,
    ROLE_LIST_SECTION_TYPE,
    RESOURCE_SUMMARY_SECTION_TYPE,
    OVERLAPPING_BOOKING_SECTION_TYPE,
    REQUIREMENTS_SECTION_TYPE,
    CRITERIA_BUDGET_SECTION_TYPE,
    WORK_HISTORY_SECTION_TYPE,
    TAB_SECTION_TYPE,
    CME_SECTION_TYPE,
    JOB_REVIEW_SECTION_TYPE
} = ENTITY_WINDOW_SECTION_TYPES;

class EntityForm extends React.Component {

    constructor(props) {
        super(props);

        this.onContextualEditStart = this.onContextualEditStart.bind(this);
        this.renderSection = this.renderSection.bind(this);
        this.onExpandCollapseSection = this.props.onExpandCollapseSection.bind(this);
        this.onRemoveEditAllFieldClick = this.onRemoveEditAllFieldClick.bind(this);
        this.shouldRenderRemoveEditAllFieldButton = this.shouldRenderRemoveEditAllFieldButton.bind(this);
        this.getIsFieldDisabled = this.getIsFieldDisabled.bind(this);
        this.getFieldReadOnly = this.getFieldReadOnly.bind(this);
    }

    onContextualEditStart(fieldInfo) {
        const { name: fieldName } = fieldInfo;
        const { form, onFieldError, onContextualEditStart } = this.props;

        form.validateFields([fieldName], (errors) => {
            onFieldError(errors);
        });

        onContextualEditStart(fieldInfo);
    }

    renderField(field, getRenderFieldOptions) {
        const {
            entity,
            uiEntity = {},
            form,
            scrollOperation,
            getPopupContainer,
            manageScrollSubscribes,
            operation
        } = this.props;

        const formProps = {
            onContextualEditStart :this.onContextualEditStart,
            onRemoveEditAllFieldClick: this.onRemoveEditAllFieldClick,
            shouldRenderRemoveEditAllFieldButton: this.shouldRenderRemoveEditAllFieldButton,
            getIsFieldDisabled: this.getIsFieldDisabled,
            getFieldReadOnly: this.getFieldReadOnly,
            scrollOperation,
            getPopupContainer,
            manageScrollSubscribes,
            operation
        };
        const buildOptions = getRenderFieldOptions(field, form, formProps);

        return this.props.getFieldControl(
            field,
            entity,
            buildOptions,
            uiEntity
        );
    }

    getFieldReadOnly(field) {
        const { operation, prepopulatedFields, getHasFunctionalAccess, getFieldInfo, uiEntity, moduleName } = this.props;
        const hasFunctionalAccess = getHasFunctionalAccess(field.fna);
        const fieldInfo = getFieldInfo(field.table, field.name);

        return (
            operation === ENTITY_WINDOW_OPERATIONS.READ
            || operation === ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT
            || !field.editable // !isFieldEditable(field, uiEntity) can be used for editableWhen cases
            || !hasFunctionalAccess
            || (prepopulatedFields.includes(field.name))
            || (uiEntity[field.name] || {}).isUIFieldDisabled === true
            || !isFieldEditable(field, uiEntity)
            || (field.readOnlyModuleNames || []).includes(moduleName)
            || isFieldAccessReadonly(fieldInfo)
        );
    }

    getIsFieldDisabled(field) {
        const { getFieldInfo, tableName } = this.props;
        const fieldInfo = getFieldInfo(tableName, field.name);

        return isFieldAccessReadonly(fieldInfo);
    }

    mapSkillsToSkillSectionsArr(skillsObject) {
        return Object.keys(skillsObject).map((key)=>{
            return {
                ...skillsObject[key],
                sectionId: key
            };
        });
    }

    mapOverlappingBookingItems(overlappingBookingsList = [], overlappingBookingsProps) {
        const { loadingSuffix, noDiaryOnDatesLabel } = overlappingBookingsProps;
        const fieldOptions = { dateFormat: getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR) };
        const fieldInfo = { dataType: FIELD_DATA_TYPES.DATE_TIME };
        let overlappingBookingItems = [];
        overlappingBookingsList.map(item => {
            let overlappingBookings = {};
            overlappingBookings.booking_guid = item.booking_guid;
            overlappingBookings.jobName = item.booking_job_description;
            overlappingBookings.bookingStartDate = formatFieldValue(item.booking_startday, fieldInfo, fieldOptions);
            overlappingBookings.bookingEndDate = formatFieldValue(item.booking_endday, fieldInfo, fieldOptions);
            overlappingBookings.bookingLoading = `${formatFieldValue(item.booking_loading, { dataType: FIELD_DATA_TYPES.FLOAT }, {})}${loadingSuffix}`;
            overlappingBookings.hasWarning = item.hasconflict;
            overlappingBookings.hasError = false;
            overlappingBookings.hasdiary = item.hasdiary;
            overlappingBookings.isSelected = false;
            overlappingBookings.carouselElementType = OVERLAPPING_BOOKING_CAROUSEL_ELEMENT_TYPE;
            overlappingBookings.noDiaryOnDatesLabel = noDiaryOnDatesLabel;
            overlappingBookingItems.push(overlappingBookings);
        });

        return overlappingBookingItems;
    }

    shouldRenderRemoveEditAllFieldButton(fieldName) {
        const groupedFields = this.props.getComplexGroupedFieldsForTable(this.props.tableName);

        return isEditAllEntity(this.props.entityId) && (!groupedFields.includes(fieldName) || fieldName === groupedFields[0]);
    }

    onRemoveEditAllFieldClick(field) {
        this.props.onBulkEditRemoveItem(field, this.props.batchFields);
    }

    getSectionContent({ sectionType, skills, fields, roleGroups, roles, sectionTotalsFields, key, subSectionItems }) {
        const {
            operation,
            skillLabels,
            hasCommentsSection,
            getCommentSectionComponent,
            getJobReviewSectionComponent,
            getUploadFilesComponent,
            skillsStaticLabels,
            isReadModeOnly,
            moduleName,
            tableName,
            entityId,
            shouldRenderField,
            hasRecentWorkHistory,
            getAriaLabelDeleteSkillButton,
            getIsSectionSysMaintained,
            talentProfilePageTransformedEnabled,
            entity
        } = this.props;

        if (sectionType === SKILLS_LIST_SECTION_TYPE) {
            const skillSections = this.mapSkillsToSkillSectionsArr(skills);
            const hiddenFieldIds = getHiddenSkillFieldIdSelector(store.getState())();

            return operation !== ENTITY_WINDOW_OPERATIONS.EDIT
                ?
                <SkillsList getAriaLabelDeleteSkillButton={getAriaLabelDeleteSkillButton} getIsSectionSysMaintained={getIsSectionSysMaintained} listItems={skills} editable={false} skillsStaticLabels={skillsStaticLabels} talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled} />
                :
                <EditSkillsWindowBody
                    {...this.props}
                    config={skillLabels}
                    skillSections={skillSections}
                    staticMessages={skillsStaticLabels}
                    renderFieldsOnly
                    hiddenFieldIds = {hiddenFieldIds}
                />;
        } else if (sectionType === COMMENTS_SECTION_TYPE && operation !== ENTITY_WINDOW_OPERATIONS.CREATE) {
            const CommentsSection = hasCommentsSection ? getCommentSectionComponent() : () => null;

            return <CommentsSection moduleName={moduleName} />;
        } else if (sectionType === JOB_REVIEW_SECTION_TYPE) {
            const JobReviewSection = getJobReviewSectionComponent();

            return <JobReviewSection moduleName={moduleName} />;
        } else if (sectionType === ATTACHMENTS_SECTION_TYPE) {
            const { getAttachmentSectionComponent } = this.props;
            const AttachmentsSection = getAttachmentSectionComponent();
            const UploadFiles = getUploadFilesComponent();
            const attachmentsProps = { moduleName, tableName, entityId };

            return operation === ENTITY_WINDOW_OPERATIONS.READ || isReadModeOnly
                ?
                <AttachmentsSection
                    {...attachmentsProps}
                    className="attachmentsSectionContentReadonly"
                    readonly
                />
                :
                <AttachmentsSection
                    {...attachmentsProps}
                    className="attachmentsSectionContent"
                    uploadButton={
                        <UploadFiles
                            {...attachmentsProps}
                        />
                    }
                />;
        } else if (sectionType === ROLE_GROUP_LIST_SECTION_TYPE) {
            return <ConnectedJobsDpRolegroupList roleGroups={roleGroups} />;
        } else if (sectionType === ROLE_LIST_SECTION_TYPE) {
            return <ConnectedRolegroupDpRolesList roles={roles}/>;
        } else if (sectionType === RESOURCE_SUMMARY_SECTION_TYPE) {
            const { getResourceSummaryComponent, moduleName, uiEntity, entityId } = this.props;
            const ResourceSummaryComponent = getResourceSummaryComponent();

            return <ResourceSummaryComponent moduleName={moduleName} uiEntity={uiEntity} entityId={entityId} />;
        } else if (sectionType === OVERLAPPING_BOOKING_SECTION_TYPE) {
            const { getOverlappingBookingsComponent, uiEntity, isSingleEntitySelected = true } = this.props;
            const OverlappingBookings = getOverlappingBookingsComponent();

            return isSingleEntitySelected ? <OverlappingBookings uiEntity={uiEntity} /> : null;
        } else if (sectionType === REQUIREMENTS_SECTION_TYPE) {
            const {
                entity,
                uiEntity = {},
                getCriteriaRenderFieldOptions,
                form,
                getFieldControl,
                entityId
            } = this.props;

            const formProps = {
                onContextualEditStart :this.onContextualEditStart,
                onRemoveEditAllFieldClick: this.onRemoveEditAllFieldClick,
                shouldRenderRemoveEditAllFieldButton: this.shouldRenderRemoveEditAllFieldButton,
                getIsFieldDisabled: this.getIsFieldDisabled,
                getFieldReadOnly: this.getFieldReadOnly
            };

            const wrappedGetCriteriaRenderFieldOptions = (fieldName) => {
                return getCriteriaRenderFieldOptions(fieldName, form, formProps);
            };

            const getShouldDisplayRequirementsSectionExplanation = () => {
                return ![ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE, ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL].includes(moduleName);
            };

            return (
                <ConnectedRequirementsSection
                    entity={entity}
                    uiEntity={uiEntity}
                    entityId={entityId}
                    getCriteriaFieldControl={getFieldControl}
                    getFieldBuildOptions={wrappedGetCriteriaRenderFieldOptions}
                    getShouldDisplaySectionExplanation={getShouldDisplayRequirementsSectionExplanation}
                />
            );

        } else if (sectionType === CME_SECTION_TYPE) {
            return (<ConnectedCMeSection entity={entity} entityId={entityId} />);
        } else if (sectionType === CRITERIA_BUDGET_SECTION_TYPE) {
            const { getNormalRenderFieldOptions, form, scrollOperation, getPopupContainer, manageScrollSubscribes } = this.props;
            const formProps = {
                onContextualEditStart :this.onContextualEditStart,
                onRemoveEditAllFieldClick: this.onRemoveEditAllFieldClick,
                shouldRenderRemoveEditAllFieldButton: this.shouldRenderRemoveEditAllFieldButton,
                getIsFieldDisabled: this.getIsFieldDisabled,
                getFieldReadOnly: this.getFieldReadOnly,
                scrollOperation,
                getPopupContainer,
                manageScrollSubscribes
            };

            const wrappedGetSectionRenderFieldOptions = (fieldName) => getNormalRenderFieldOptions(fieldName, form, formProps);

            return (
                <ConnectedCriteriaRoleBudgetSection
                    {...this.props}
                    componentKey={key}
                    primaryFields={fields}
                    totalsFields={sectionTotalsFields}
                    getSectionRenderFieldOptions={wrappedGetSectionRenderFieldOptions}
                />
            );
        } else if (sectionType === WORK_HISTORY_SECTION_TYPE) {
            return hasRecentWorkHistory ? (
                <div className="workHistory-dataGrid">
                    <ConnectedWorkHistoryDataGrid
                        {...this.props}
                        alias={moduleName}
                        getDataGridPageState={getWorkHistoryPageDataForEntityWindow}
                        wrappedGetPageTableDatasByAliasSelector={entityWindowWorkHistoryPagedDataSelector} />
                </div>
            ) : null;
        } else {
            const {
                tableName,
                getFeatureFlag,
                getNormalRenderFieldOptions,
                getFieldMessages
            } = this.props;

            const isEnabledBudgetHoursAndRevenue = getFeatureFlag ? getFeatureFlag(FEATURE_FLAGS.BUDGET_HOURS_AND_REVENUE) : false;

            const displayFields = fields.reduce((accumulator, field) => {
                if ((shouldRenderField && shouldRenderField(this.props, field, isEnabledBudgetHoursAndRevenue)) || !shouldRenderField) {
                    if (isEnabledBudgetHoursAndRevenue) {
                        const fieldMessages = getFieldMessages(tableName, field.name);
                        if (fieldMessages) {
                            field['fieldMessages'] = fieldMessages;
                        }
                    }
                    accumulator.push(this.renderField(field, getNormalRenderFieldOptions));
                }

                return accumulator;
            }, []);

            const displaySubSectionItems = isEnabledBudgetHoursAndRevenue && subSectionItems && subSectionItems.length > 0
                ? subSectionItems.reduce((accumulator, section, index) => {
                    let sectionFieldsCount = 0;
                    if (section.fields && section.fields.length > 0) {
                        section.fields.forEach((field) => {
                            if ((shouldRenderField && shouldRenderField(this.props, field, isEnabledBudgetHoursAndRevenue)) || !shouldRenderField) {
                                sectionFieldsCount += 1;
                            }
                        });

                        if (sectionFieldsCount > 0) {
                            const id = `subSection_${index}`;
                            accumulator.push(this.renderSection({
                                ...section,
                                id: id,
                                className: 'sub-section',
                                collapsed: null,
                                isSubSectionTitle: true
                            }));
                        }
                    }

                    return accumulator;
                }, [])
                : [];

            if (displayFields.length > 0 && displaySubSectionItems.length > 0) {
                return (
                    <>
                        {displayFields}
                        {displaySubSectionItems}
                    </>
                );
            }

            return displayFields;
        }
    }

    getEntityFormSectionComponent(sectionType) {
        switch (sectionType) {
            case REQUIREMENTS_SECTION_TYPE:
                return ConnectedRequirementsEntityFormSection;
            case WORK_HISTORY_SECTION_TYPE:
                return ConnectedWorkHistroyEntityFormSection;
            case TAB_SECTION_TYPE:
                return EntityFormTabSection;
            default:
                return EntityFormSection;
        }
    }

    renderSection(section) {
        const {
            key,
            title,
            shouldDisplayTitle,
            collapsed,
            hasErrors,
            messages,
            hideContent = false,
            shouldDisplayWarning = false,
            id,
            className,
            subSections = [],
            subSectionItems = [],
            tabs = {},
            isSubSectionTitle,
            additionalTitleComponent = (<> </>)
        } = section;
        const { tableName, entityId, entity, moduleName, getMessageAreaComponent, staticLabels } = this.props;
        const EntityFormSectionComponent = this.getEntityFormSectionComponent(section.sectionType);
        const hasContentElements = !collapsed && hideContent == false;

        const contentElements = subSections.length > 1
            ? hasContentElements && subSections.map(section => {
                const content = this.getSectionContent(section);

                return Array.isArray(content) ? <Row key={`subSection_${key}_${entityId}`} componentKey={section.key}>{content}</Row> : content;
            })
            : hasContentElements && this.getSectionContent(section);

        return (
            <EntityFormSectionComponent
                id={id}
                className={className}
                key={`${key}_section_${entityId}`}
                shouldDisplayTitle={shouldDisplayTitle}
                collapsed={collapsed}
                sectionKey={key}
                componentKey={key}
                title={title}
                registerLinkRef={this.props.registerLinkRef}
                hasErrors={hasErrors}
                messages={collapsed ? [] : messages}
                entityId={entityId}
                toggleExpandCollapseSection={() => this.onExpandCollapseSection(tableName, key, !collapsed)}
                shouldDisplayWarning = {shouldDisplayWarning}
                entity={entity}
                moduleName={moduleName}
                tableName={tableName}
                getMessageAreaComponent={getMessageAreaComponent}
                tabs={tabs}
                staticLabels={staticLabels}
                subSectionItems={subSectionItems}
                isSubSectionTitle={isSubSectionTitle}
                additionalTitleComponent = {additionalTitleComponent}
            >
                {contentElements}
            </EntityFormSectionComponent>
        );
    }

    render() {
        const {
            operation,
            attachmentIds,
            shouldRenderSection,
            isReadModeOnly,
            moduleName,
            entity,
            registerLink
        } = this.props;

        const sectionsLinks = [];
        const sections = this.props.sections.reduce((accumulator, section, index) => {
            const sectionContent = this.getSectionContent(section);
            if (shouldRenderSection(section, operation, attachmentIds, isReadModeOnly, moduleName, entity, sectionContent)) {
                const id = `section_${index}`;
                accumulator.push(this.renderSection({
                    ...section,
                    id: id,
                    className: 'section',
                    isSubSectionTitle: false
                }));
                sectionsLinks.push({ id: id, title: section.title });
            }

            return accumulator;
        }, []);

        if (registerLink) {
            registerLink(sectionsLinks);
        }

        return (
            <Form layout={'horizontal'}>
                {sections}
            </Form>
        );
    }
}
EntityForm.propTypes = EntityFormProps;

const onFieldsChange = (props, changedFields) => {
    let skillFields = {};
    let entityFields = {};

    Object.keys(changedFields).forEach((fieldId) => {
        if (changedFields[fieldId].skillId)
            skillFields = {
                ...skillFields,
                [fieldId]: changedFields[fieldId]
            };
        else
            entityFields = {
                ...entityFields,
                [fieldId]: changedFields[fieldId]
            };
    });

    return (props.onFieldsChange(props, entityFields, props.entityId)) || (props.onSkillFieldsChange(props, skillFields));
};

const EntityFormCreated = Form.create({
    onFieldsChange,
    mapPropsToFields: (props) => props.mapPropsToFields(props)
})(EntityForm);

export {
    EntityForm,
    EntityFormCreated
};