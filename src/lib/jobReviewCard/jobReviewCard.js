import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from 'antd';
import './JobReviewCard.less';

/**
 * Content for the Reviews section (for use inside a main section/accordion).
 *
 * @param {Object} props
 * @param {number} props.reviewCount
 * @param {Function} props.onSubmitReview
 */
const JobReviewCard = ({
    reviewCount,
    onSubmitReview
}) => (
    <div className="job-review-section">
        <div className="job-review-section__row">
            <Typography.Text className="ant-col ant-col-8 job-review-section__label">
                Skills reviews
            </Typography.Text>

            <div className="ant-col ant-col-12">
                <Typography.Text className="job-review-section__count">
                    {reviewCount} resources reviewed
                </Typography.Text>
                <br />
                <div>
                    <Typography.Link
                        className="job-review-section__submit"
                        onClick={onSubmitReview}
                    >
                        Submit skills review
                    </Typography.Link>
                </div>
            </div>
        </div>
    </div>

);

JobReviewCard.propTypes = {
    reviewCount: PropTypes.number.isRequired,
    onSubmitReview: PropTypes.func.isRequired
};

export default JobReviewCard;
