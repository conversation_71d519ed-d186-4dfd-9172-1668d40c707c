import { ENTITY_WINDOW_SECTION_TYPES } from '../../../constants/entityWindowConsts';
import { ConnectedAttachmentsSection } from '../../connectedAttachments/connectedAttachmentsSection';
import { ConnectedUploadFiles } from '../../connectedAttachments/connectedUploadFiles';
import { ConnectedCommentsSection } from '../comments/connectedCommentSection';
import { ConnectedResourceSummary } from '../../connectedEntityWindow/resourceSummary/connectedResourceSummary';
import { skillsMapStateToProps } from '../skills/stateToProps';
import { getAriaLabelDeleteSkillButtonSelector, getSkillsStaticMessages } from '../../../selectors/editSkillsWindowSelectors';
import { ConnectedOverlappingBookings } from '../overlappingBookings/connectedOverlappingBookings';
import { ConnectedCMeSection } from '../../connectedCMe/connectedCMeSection';
import { isSectionSysMaintainedSelector } from '../../../selectors/skillStructureSelectors';
import ConnectedJobReviewCard from '../../connectedJobReviewCard/connectedJobReviewCard';

const getCommentSectionComponent = () => {
    return ConnectedCommentsSection;
};

const getJobReviewSectionComponent = () => {
    return ConnectedJobReviewCard;
};

const getCMeSectionComponent = () => {
    return ConnectedCMeSection;
};

const getAttachmentSectionComponent = () => {
    return ConnectedAttachmentsSection;
};
const getUploadFilesComponent = () => {
    return ConnectedUploadFiles;
};

const getResourceSummaryComponent = () => {
    return ConnectedResourceSummary;
};

const getOverlappingBookingsComponent = () => {
    return ConnectedOverlappingBookings;
};

const getPropsForSection = (section, context) => {
    const { state, selectorInstance, moduleName, entityId } = context;
    let sectionProps = {};

    if (ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE === section.sectionType) {
        const { attachments } = state;
        const { getUIEntityAttachmentIds } = selectorInstance.getSelectors();
        sectionProps = {
            ...sectionProps,
            getUploadFilesComponent,
            attachmentIds: getUIEntityAttachmentIds({
                attachments,
                moduleName,
                entityId
            }),
            getAttachmentSectionComponent
        };
    }

    if (ENTITY_WINDOW_SECTION_TYPES.COMMENTS_SECTION_TYPE === section.sectionType) {
        sectionProps = {
            ...sectionProps,
            getCommentSectionComponent
        };
    }

    if (ENTITY_WINDOW_SECTION_TYPES.JOB_REVIEW_SECTION_TYPE === section.sectionType) {
        sectionProps = {
            ...sectionProps,
            getJobReviewSectionComponent
        };
    }

    if (ENTITY_WINDOW_SECTION_TYPES.SKILLS_LIST_SECTION_TYPE === section.sectionType) {
        sectionProps = {
            ...sectionProps,
            ...skillsMapStateToProps(state),
            skillsStaticLabels: getSkillsStaticMessages(state),
            getAriaLabelDeleteSkillButton: getAriaLabelDeleteSkillButtonSelector(state),
            getIsSectionSysMaintained: (sectionId) => isSectionSysMaintainedSelector(state)(sectionId)
        };
    }

    if (ENTITY_WINDOW_SECTION_TYPES.CME_SECTION_TYPE === section.sectionType) {
        sectionProps = {
            ...sectionProps,
            getCMeSectionComponent
        };
    }

    if (ENTITY_WINDOW_SECTION_TYPES.RESOURCE_SUMMARY_SECTION_TYPE === section.sectionType) {
        sectionProps = {
            ...sectionProps,
            getResourceSummaryComponent
        };
    }

    if (ENTITY_WINDOW_SECTION_TYPES.OVERLAPPING_BOOKING_SECTION_TYPE === section.sectionType) {
        sectionProps = {
            ...sectionProps,
            getOverlappingBookingsComponent
        };
    }

    return sectionProps;
};

const getSectionSpecificProps = context => {
    const { sections } = context;
    const { sections: _omitted, ...restOfContext } = context;

    const sectionProps = sections.reduce((sectionProps, section) => {
        return (sectionProps = {
            ...sectionProps,
            ...getPropsForSection(section, restOfContext)
        });
    }, {});

    return sectionProps;
};

export default getSectionSpecificProps;
