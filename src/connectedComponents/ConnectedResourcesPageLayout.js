import { connect } from "react-redux";
import { omit } from '../utils/commonUtils';
import { withHotKeys } from '../lib/hotKeys/hotKeys';

import EntityListLayout from "../layouts/entity-list-layout";

import { resourcesPageHotKeysConfigByStrategy } from "../state/hotKeys/resourcesPage/hotKeysConfig";
import { getHotKeyConfig, triggerHotKeyAction } from "../utils/hotKeys/jobsPageHotKeysUtils";
import { getResourcesPageFilterPaneCollapsed } from '../selectors/dataGridPageSelectors';
import { getApplicationAccessSelector } from '../selectors/userEntityAccessSelectors';
import { getSelectedEntitiesSelector } from '../selectors/commonSelectors';
import store from '../store/configureStore';
import { RESOURCES_PAGE_ALIAS } from "../constants/resourcesPageConsts";

const mapStateToProps = state => {
    const getApplicationAccessSelectorWrapped = getApplicationAccessSelector(state);

    return {
        filterPaneHidden: getResourcesPageFilterPaneCollapsed(state),
        hotKeysConfig: resourcesPageHotKeysConfigByStrategy,
        getHotKeyConfig,
        getUserHasAccess: getApplicationAccessSelectorWrapped
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        triggerHotKey: (hotKey, state, context) => {
            triggerHotKeyAction(state, dispatch, hotKey, context);
        }
    };
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
    return {
        ...omit(
            propsFromState,
            [
                'fnas',
                'getHotKeyConfig'
            ]
        ),
        ...ownProps,
        triggerHotKey(hotKey) {
            const { hotKeysConfig, getUserHasAccess } = propsFromState;
            const { functionalAccess, entityAccess, actionKey } = getHotKeyConfig(hotKey) || { functionalAccess: null };

            const state = store.getState();
            const selectedResourcePane = state.entityWindow.window.resourcesPageDetailsPane;

            const { tableName, entityId } = selectedResourcePane;
            const { ids = [] } = getSelectedEntitiesSelector(state, RESOURCES_PAGE_ALIAS);
            const entityArr = entityId ? [entityId] : ids;

            if (getUserHasAccess(tableName, entityArr, entityAccess, functionalAccess, actionKey)) {
                propsFromDispatch.triggerHotKey(hotKey, state, hotKeysConfig);
            }
        }
    };
};

const ConnectedResourcesPageLayout = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(withHotKeys(EntityListLayout));

export { ConnectedResourcesPageLayout };
