import { connect } from 'react-redux';
import RolegroupListLayout from '../layouts/role-group-list-layout';
import { getApplicationAccessSelector } from '../selectors/userEntityAccessSelectors';
import { omit } from '../utils/commonUtils';
import { getHotKeyConfig } from '../utils/hotKeys/jobsPageHotKeysUtils';
import { getCurrentPageTitleSelector } from '../selectors/navigationSelectors';
import { JOBS_PAGE_ALIAS } from '../constants';
import { LIST_PAGE_ALIAS } from '../constants/listPageConsts';

const mapStateToProps = (state, ownProps) => {
  const { match } = ownProps;
  const currentPath = match.path;
  const getState = () => state;
  const getApplicationAccessSelectorWrapped =
    getApplicationAccessSelector(state);

  // Determine the correct page alias based on current path
  const masterPageAlias = currentPath.includes('/list')
    ? LIST_PAGE_ALIAS
    : JOBS_PAGE_ALIAS;
  const pageTitle = getCurrentPageTitleSelector(state)(masterPageAlias);

  return {
    currentPath,
    getHotKeyConfig,
    getState,
    getUserHasAccess: getApplicationAccessSelectorWrapped,
    pageTitle,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {};
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
  return {
    ...omit(propsFromState, ['getState', 'fnas', 'getHotKeyConfig']),
    ...ownProps,
  };
};

const ConnectedRolegroupListLayout = connect(
  mapStateToProps,
  mapDispatchToProps,
  mergeProps
)(RolegroupListLayout);

export { ConnectedRolegroupListLayout };
