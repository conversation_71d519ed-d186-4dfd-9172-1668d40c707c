import { connect } from 'react-redux';
import RolegroupListLayout from '../layouts/role-group-list-layout';
import { getApplicationAccessSelector } from '../selectors/userEntityAccessSelectors';
import { omit } from '../utils/commonUtils';
import { getHotKeyConfig } from '../utils/hotKeys/jobsPageHotKeysUtils';
import { getCurrentPageTitleSelector } from '../selectors/navigationSelectors';
import { JOBS_PAGE_ALIAS } from '../constants';

const mapStateToProps = (state, ownProps) => {
  const { match } = ownProps;
  const currentPath = match.path;
  const getState = () => state;
  const getApplicationAccessSelectorWrapped =
    getApplicationAccessSelector(state);

  // Always use JOBS_PAGE_ALIAS since getCurrentPageTitleSelector handles feature flag logic internally
  const pageTitle = getCurrentPageTitleSelector(state)(JOBS_PAGE_ALIAS);

  return {
    currentPath,
    getHotKeyConfig,
    getState,
    getUserHasAccess: getApplicationAccessSelectorWrapped,
    pageTitle,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {};
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
  return {
    ...omit(propsFromState, ['getState', 'fnas', 'getHotKeyConfig']),
    ...ownProps,
  };
};

const ConnectedRolegroupListLayout = connect(
  mapStateToProps,
  mapDispatchToProps,
  mergeProps
)(RolegroupListLayout);

export { ConnectedRolegroupListLayout };
