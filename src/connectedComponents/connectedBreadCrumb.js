import { connect } from 'react-redux';
import { pushUrl } from '../actions/navigateActions';
import {
  breadcrumbLevelsMap,
  subPagesOptionsMap,
} from '../constants/breadcrumbConsts';
import BreadCrumb, { buildBreadcrumbOptions } from '../lib/breadCrumb';
import { getCommandBarConfigSelector } from '../selectors/commandBarSelectors';
import { JOBS_PAGE_ALIAS } from '../constants/jobsPageConsts';

export const mapStateToProps = (state, ownProps) => {
  const { pageAlias, masterPageOptions, getPageState } = ownProps;
  const { masterPageAlias, masterPageDisplayName } = masterPageOptions;
  const { params } = state[pageAlias].pageState;
  const masterPageParams = (getPageState(state, masterPageAlias) || {}).params;

  // Always use JOBS_PAGE_ALIAS for command bar config since LIST_PAGE doesn't have its own config
  const commandBarPageAlias = JOBS_PAGE_ALIAS;
  const config = getCommandBarConfigSelector(state, commandBarPageAlias);
  const aliasPageDisplayName = config.pageTitleSection.pageTitle;

  const breadcrumbOptions = params
    ? buildBreadcrumbOptions(
        breadcrumbLevelsMap[pageAlias],
        params,
        subPagesOptionsMap[masterPageDisplayName],
        masterPageParams,
        masterPageAlias,
        aliasPageDisplayName
      )
    : [];
  const sepratatorIconType = 'slash';

  return {
    breadcrumbOptions,
    sepratatorIconType,
  };
};

export const mapDispatchToProps = (dispatch) => {
  return {
    onClick: (newParams, page) => {
      dispatch(pushUrl(newParams, page));
    },
  };
};

const ConnectedBreadcrumb = connect(
  mapStateToProps,
  mapDispatchToProps
)(BreadCrumb);

export default ConnectedBreadcrumb;
