import { connect } from 'react-redux';
import RolegroupDetailsLayout from '../layouts/role-group-details-layout';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE } from '../constants';
import { LIST_PAGE_ALIAS } from '../constants/listPageConsts';
import { updatePageParams } from '../actions/pageStateActions';
import { getApplicationAccessSelector } from '../selectors/userEntityAccessSelectors';
import { EDIT_FNAS_PER_TABLENAME } from '../constants/tablesConsts';
import { ENTITY_ACCESS_TYPES } from '../constants/entityAccessConsts';
import { TABLE_NAMES } from '../constants/globalConsts';
import { getCurrentPageTitleSelector } from '../selectors/navigationSelectors';

const mapStateToProps = (state, ownProps) => {
  const { match } = ownProps;
  const currentPath = match.path;
  const roleGroupDescription =
    state.rolegroupDetailsPage.pageState.params &&
    (state.rolegroupDetailsPage.pageState.params.changedRolegroupName ||
      state.rolegroupDetailsPage.pageState.params.rolegroupName);
  const entityId =
    state.rolegroupDetailsPage.pageState.params &&
    (state.rolegroupDetailsPage.pageState.params.rolerequestgroupGuid || '');
  const hasResults =
    Object.keys(state.rolegroupDetailsPage.tableDatas).length > 0;
  const getApplicationAccessSelectorWrapped =
    getApplicationAccessSelector(state);

  // Determine the correct page alias based on current path
  const masterPageAlias = currentPath.includes('/list')
    ? LIST_PAGE_ALIAS
    : JOBS_PAGE_ALIAS;
  const pageTitle = getCurrentPageTitleSelector(state)(masterPageAlias);

  const roleGroupDetailsConfig = {
    entityAccess: ENTITY_ACCESS_TYPES.EDIT,
    tableName: TABLE_NAMES.ROLEREQUESTGROUP,
    functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
    entityIds: [entityId],
  };

  return {
    currentPath,
    roleGroupDescription,
    hasResults,
    roleGroupDetailsConfig,
    getUserHasAccess: getApplicationAccessSelectorWrapped,
    pageTitle,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    onRolegroupNameChanged: (changedRolegroupName) => {
      const params = {
        changedRolegroupName,
      };

      dispatch(updatePageParams(ROLE_GROUP_DETAILS_PAGE, params));
    },
  };
};

const ConnectedRolegroupDetailsLayout = connect(
  mapStateToProps,
  mapDispatchToProps
)(RolegroupDetailsLayout);

export { ConnectedRolegroupDetailsLayout };
