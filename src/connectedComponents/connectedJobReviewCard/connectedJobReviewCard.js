import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import JobReviewCard from '../../lib/jobReviewCard/jobReviewCard';

/**
 * Redux container for connecting JobReviewCard with global state.
 *
 * @param {Object} props
 * @param {number} props.reviewCount
 * @param {function} props.fetchReviews
 * @param {function} props.submitReview
 */
class ConnectedJobReviewCard extends React.Component {
  static propTypes = {
      reviewCount: PropTypes.number.isRequired,
      fetchReviews: PropTypes.func.isRequired,
      submitReview: PropTypes.func.isRequired
  };

  handleSettingsClick = () => {
      // TBD
      console.log('Settings clicked');
  };

  handleSubmitReview = () => {
      // this.props.submitReview();
      // TBD
      console.log('Settings clicked');
  };

  render() {
      const { reviewCount } = this.props;

      return (
          <JobReviewCard
              reviewCount={reviewCount}
              onSettingsClick={this.handleSettingsClick}
              onSubmitReview={this.handleSubmitReview}
          />
      );
  }
}

const mapStateToProps = (state) => ({
    reviewCount: state?.reviews?.reviewCount || 0 // Assuming reviewCount is stored in state.reviews
});

const mapDispatchToProps = {
    fetchReviews: () => {}, // Placeholder for actual fetchReviews action
    submitReview: () => {} // Placeholder for actual submitReview action
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(ConnectedJobReviewCard);
