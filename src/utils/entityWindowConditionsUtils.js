import { DETAILS_PANE_TAB_KEYS, ENTITY_WINDOW_MODULES, JOBS_PAGE_ALIAS, PLANNER_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE, ROLE_INBOX_PAGE_ALIAS, TABLE_NAMES, UNASSIGNED_BOOKINGS_RESOURCE } from '../constants';
import { ENTITY_ACCESS_TYPES, ENTITY_ACTION_KEYS } from '../constants/entityAccessConsts';
import { BOOKING_SERIES_GUID, JOB_END_DATE, JOB_LONG_RUNNING_OPERATION, ROLEMARKETPLACE_FIELDS, ROLEREQUESTRESOURCE_FIELDS, ROLEREQUEST_FIELDS } from '../constants/fieldConsts';
import { ROLE_ITEM_STATUS_KEYS } from '../constants/rolesConsts';
import { EDIT_FNAS_PER_TABLENAME, ROLEREQUEST_WORKFLOW_ACCESS_TYPES } from '../constants/tablesConsts';
import { getBatchWindowHasCombinedRoleTypesSelection, roleResourcesHasUnassignedResource, roleResourcesReadyForBookingRequest } from './entityWindowUtils';
import { ENTITY_WINDOW_OPERATIONS, ENTITY_WINDOW_SECTION_KEYS, ENTITY_WINDOW_SECTION_TYPES } from '../constants/entityWindowConsts';
import { getIsCriteriaRole, getCriteriaRoleAssignedResourceIds, canAssignCriteriaRoleRoleByFixedTime } from './roleRequestsUtils';
import { getPageRoleRequestStatusDescriptionSelector } from '../selectors/roleRequestStatusSelectors';
import { getSuggestedResourceHasAssignToRoleAccess } from './userEntityAccessUtils';
import { getData, isEmptyObject } from './commonUtils';
import { isRecentWorkGridHasData } from './workHistoryUtils';
import { isFieldAccessHidden } from './fieldControlUtils';
import { getIsMarketplaceRoleApplyableSelector, getIsMarketplaceRoleWithdralableSelector } from '../selectors/roleMarketplaceCustomConditionsSelectors';
import { getCurrentPageAliasSelector } from '../selectors/navigationSelectors';
import { getSelectedWorkspaceSettings, getWorkspaceMasterRecPlannerDataGuid, getWorkspaceMasterRecTableName, getWorkspaceSubRecPlannerDataGuid } from '../selectors/workspaceSelectors';
import { EDIT_REPEAT_BOOKING_TYPE } from '../constants/globalConsts';

const singleResourceGuidModules = [
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
    ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED,
    ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL
];

const multipleResourceGuidModules = [
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL
];

const getRoleRequestResourcesIds = (context) => {
    const { entityWindow = {}, moduleName } = context;
    const { entity = {} } = entityWindow;
    const { rolerequest_resource_guids = [], rolerequest_resource_guid } = entity;
    let resourceIds = [];

    if (singleResourceGuidModules.includes(moduleName) && rolerequest_resource_guid != null) {
        resourceIds = [rolerequest_resource_guid];
    } else if (multipleResourceGuidModules.includes(moduleName) && rolerequest_resource_guids && rolerequest_resource_guids.length > 0) {
        resourceIds = rolerequest_resource_guids;
    } else if (moduleName === ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE) {
        (Object.values(entityWindow.windows) || []).forEach(({ entity }) => resourceIds.push(entity.rolerequest_resource_guid));
    } else if (moduleName === ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE) {
        (Object.values(entityWindow.windows) || []).forEach(({ entity }) => resourceIds.push((entity || {}).rolerequest_resource_guid));
    }

    return resourceIds;
};

export const allRoleResourcesAreUnassigned = (resourceIds) => {
    const allRolesLackResource = resourceIds.every(id => id == null || id === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID);

    return allRolesLackResource;
};

export const roleRequestHasUnassignedResourcesCondition = {
    type: 'roleRequestHasUnassignedResourcesCondition',
    conditionCheck: (context) => {
        let resourceIds = getRoleRequestResourcesIds(context);
        const hasUnassignedResource = roleResourcesHasUnassignedResource(resourceIds);

        return hasUnassignedResource;
    }
};

export const roleRequestHasInvalidResourceCondition = {
    type: 'roleRequestHasInvalidResourceCondition',
    conditionCheck: (context) => {
        const { entityWindow = {} } = context;
        const { entity = {}, operation } = entityWindow;

        let result = true;
        const isCreateModal = operation === ENTITY_WINDOW_OPERATIONS.CREATE;

        if (isCreateModal) {
            if (getIsCriteriaRole(entity)) {
                result = false;
            } else {
                let resourceIds = getRoleRequestResourcesIds(context);
                const hasValidResource = roleResourcesReadyForBookingRequest(resourceIds);

                result = !hasValidResource;
            }
        }

        return result;
    }
};

export const cannotAssignResourceToRolerequestAssigneeLimitCondition = {
    type: 'cannotAssignResourceToRolerequestAssigneeLimitCondition',
    conditionCheck: (context) => {
        const { rootState: state, activeRoleEntity = {} } = context;
        const getRoleStatusDescription = getPageRoleRequestStatusDescriptionSelector(state);
        const {
            [ROLEREQUEST_FIELDS.GUID]: roleGuid
        } = activeRoleEntity;
        const isCriteria = getIsCriteriaRole(activeRoleEntity);
        const { roleAssigneesState } = state;
        const assignedResources = roleAssigneesState[roleGuid] || [];

        return !(isCriteria && canAssignCriteriaRoleRoleByFixedTime(activeRoleEntity, assignedResources, getRoleStatusDescription));
    }
};

export const cannotAssignResourceToRolerequestCondition = {
    type: 'cannotAssignResourceToRolerequestCondition',
    conditionCheck: (context) => {
        const { rootState: state, detailsPane, entityWindow, activeRoleEntity = {}, useMultipleAssignees } = context;
        const { selectedTabKey } = detailsPane;
        const {
            [ROLEREQUEST_FIELDS.GUID]: roleGuid,
            [ROLEREQUEST_FIELDS.STATUS_GUID]: statusGuid
        } = activeRoleEntity;

        const suggestedResourceGuid = entityWindow.entityId;
        const roleAssignees = state.roleAssigneesState[roleGuid] || [];

        const assignedResourcesGuids = getCriteriaRoleAssignedResourceIds(useMultipleAssignees, roleAssignees, activeRoleEntity);
        const statusDescription = getPageRoleRequestStatusDescriptionSelector(state)(statusGuid);

        const roleSuggestedResources = (state.suggestedResources || {})[roleGuid] || {};
        const resourceHasAssignToRoleAccess = getSuggestedResourceHasAssignToRoleAccess(suggestedResourceGuid, roleSuggestedResources);
        const hasAssignees = assignedResourcesGuids.length;

        return assignedResourcesGuids.includes(suggestedResourceGuid) ||
        selectedTabKey != DETAILS_PANE_TAB_KEYS.SUGGESTIONS_KEY ||
        !resourceHasAssignToRoleAccess ||
        statusDescription !== ROLE_ITEM_STATUS_KEYS.REQUESTED ||
        cannotAssignResourceToRolerequestAssigneeLimitCondition.conditionCheck(context) ||
        (hasAssignees && !useMultipleAssignees);
    }
};

export const cannotUnassignResourceFromRolerequestCondition = {
    type: 'cannotUnassignResourceFromRolerequestCondition',
    conditionCheck: (context) => {
        const { rootState: state, entityWindow, detailsPane, activeRoleEntity = {}, useMultipleAssignees } = context;
        const { selectedTabKey } = detailsPane;
        const {
            [ROLEREQUEST_FIELDS.GUID]: roleGuid,
            [ROLEREQUEST_FIELDS.STATUS_GUID]: statusGuid
        } = activeRoleEntity;

        const suggestedResourceGuid = entityWindow.entityId;
        const roleAssignees = state.roleAssigneesState[roleGuid] || [];

        const assignedResourcesGuids = getCriteriaRoleAssignedResourceIds(useMultipleAssignees, roleAssignees, activeRoleEntity);
        const roleStatusDescription = getPageRoleRequestStatusDescriptionSelector(state)(statusGuid);

        const assigneeData = roleAssignees.find(assignee => assignee[ROLEREQUESTRESOURCE_FIELDS.RESOURCE_GUID] === suggestedResourceGuid) || {};
        const assigneeStatusDescription = getPageRoleRequestStatusDescriptionSelector(state)(assigneeData[ROLEREQUESTRESOURCE_FIELDS.STATUS_GUID]);

        const roleSuggestedResources = (state.suggestedResources || {})[roleGuid] || {};
        const resourceHasAssignToRoleAccess = getSuggestedResourceHasAssignToRoleAccess(suggestedResourceGuid, roleSuggestedResources);

        return !assignedResourcesGuids.includes(suggestedResourceGuid) ||
            assigneeStatusDescription !== ROLE_ITEM_STATUS_KEYS.REQUESTED ||
            roleStatusDescription !== ROLE_ITEM_STATUS_KEYS.REQUESTED ||
            selectedTabKey != DETAILS_PANE_TAB_KEYS.SUGGESTIONS_KEY ||
            !resourceHasAssignToRoleAccess;
    }
};

export const cannotEditResourceViaSuggestedTabCondition = {
    type: 'cannotEditResourceViaSuggestedTabCondition',
    conditionCheck: (context) => {
        const { detailsPane } = context;
        const { selectedTabKey } = detailsPane;

        return selectedTabKey === DETAILS_PANE_TAB_KEYS.SUGGESTIONS_KEY || selectedTabKey === DETAILS_PANE_TAB_KEYS.ROLE_GROUP_KEY;
    }
};

export const cannotEditRoleGroupCondition = {
    type: 'cannotEditRoleGroupCondition',
    conditionCheck: (context) => {
        const { detailsPane } = context;
        const { selectedTabKey } = detailsPane;

        return selectedTabKey !== DETAILS_PANE_TAB_KEYS.ROLE_GROUP_KEY;
    }
};

export const cannotEditEntityCondition = {
    type: 'cannotEditEntityCondition',
    conditionCheck: (context) => {
        const { entityWindow, getAccessibleEntitiesIds = () => [] } = context;
        const { tableName, batchIds } = entityWindow;
        let result = getAccessibleEntitiesIds(tableName, batchIds, ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[tableName], ENTITY_ACTION_KEYS.EDIT).length === 0;

        if (tableName === TABLE_NAMES.ROLEREQUEST) {
            result = result || getBatchWindowHasCombinedRoleTypesSelection(entityWindow);
        }

        return result;
    }
};

export const cannotSaveEntityWhenFormHasError = {
    type: 'cannotSaveEntityWhenFormHasError',
    conditionCheck: (context) => {
        const { getEntityWindowError, moduleName } = context;
        const { code } = getEntityWindowError(moduleName) || {};
        const hasCodeValue = Number.isInteger(code);

        return hasCodeValue;
    }
};

export const cannotPublishCustomCondition = {
    type: 'cannotPublishCustomCondition',
    conditionCheck: (context) => {
        const { getEntityWindowError, moduleName, entityWindow } = context;
        const { entity } = entityWindow;
        const { code } = getEntityWindowError(moduleName) || {};
        const {
            [ROLEMARKETPLACE_FIELDS.CATEGORY_GUID]: categoryId,
            [ROLEMARKETPLACE_FIELDS.PUBLISHEDON]: publishedOnDate
        } = entity;

        const hasCodeValue = Number.isInteger(code);

        let result = hasCodeValue;

        if (categoryId === null || publishedOnDate === null) {
            result = true;
        }

        return result;
    }
};

export const noRequirementsAddedCondition = {
    type: 'noRequirementsAddedCondition',
    conditionCheck: (context) => {
        const { entityWindow: { entityId }, roleRequirementsMap } = context;
        const { criteriaDetails } = roleRequirementsMap[entityId] || {};

        return isEmptyObject(criteriaDetails || {});
    }
};

const getShouldHideEditRoleByTypeAction = (actionKey, context) => {
    const { entityWindow, getAccessibleEntitiesIds = () => [] } = context;
    const { tableName, batchIds } = entityWindow;

    const accessibleIds = getAccessibleEntitiesIds(tableName, batchIds, ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[tableName], actionKey);

    return accessibleIds.length === 0 || !getBatchWindowHasCombinedRoleTypesSelection(entityWindow);
};

export const cannotEditRoleByNameCondition = {
    type: 'cannotEditRoleByNameCondition',
    conditionCheck: (context) => {
        const { entityWindow } = context;
        const { tableName } = entityWindow;

        let result = true;

        if (tableName === TABLE_NAMES.ROLEREQUEST) {
            result = getShouldHideEditRoleByTypeAction(ENTITY_ACTION_KEYS.EDIT_ROLE_BY_NAME, context);
        }

        return result;
    }
};

export const cannotEditRoleByCriteriaCondition = {
    type: 'cannotEditRoleByCriteriaCondition',
    conditionCheck: (context) => {
        const { entityWindow } = context;
        const { tableName } = entityWindow;

        let result = true;

        if (tableName === TABLE_NAMES.ROLEREQUEST) {
            result = getShouldHideEditRoleByTypeAction(ENTITY_ACTION_KEYS.EDIT_ROLE_BY_CRITERIA, context);
        }

        return result;
    }
};

export const shouldRenderEditBookingSeriesCondition = {
    type: 'shouldRenderEditBookingSeriesCondition',
    conditionCheck: (context) => {
        const { entityWindow, tableName, isReadModeOnly, moduleName } = context;
        const { entity } = entityWindow;

        return isReadModeOnly
            && tableName === TABLE_NAMES.BOOKING
            && entity[BOOKING_SERIES_GUID]
            && moduleName === ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL ? false : true;
    }
};

export const roleRequestBookingFunctionalAccessExcludeCondition = {
    type: 'roleRequestBookingFunctionalAccessExcludeCondition',
    conditionCheck : (context) => {
        const { getAccessibleEntitiesIds, entityId } = context;

        const hasAccess = getAccessibleEntitiesIds(
            TABLE_NAMES.ROLEREQUEST,
            [entityId],
            ENTITY_ACCESS_TYPES.EDIT,
            null,
            ENTITY_ACTION_KEYS.MAKE_LIVE,
            ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MAKE_LIVE
        ).length > 0;

        return !hasAccess;
    }
};

// work on better solution with following dev bug
export const hideEntityWindowEditDividersCondition = {
    type: 'hideEntityWindowEditDividersCondition',
    conditionCheck: (context) => {
        return cannotEditEntityCondition.conditionCheck(context)
            && cannotEditRoleByNameCondition.conditionCheck(context)
            && cannotEditRoleByCriteriaCondition.conditionCheck(context);
    }
};

export const cannotRejectRequestCondition = {
    type: 'cannotRejectRequestCondition',
    conditionCheck: (context) => {
        const { entityWindow = {} } = context;

        return (entityWindow.batchIds || []).length > 1;
    }
};

export const cannotManageBudgetRequestCondition = {
    type: 'cannotManageBudgetRequestCondition',
    conditionCheck: (context) => {
        const { entityWindow = {} } = context;

        return (entityWindow.batchIds || []).length > 1;
    }
};

export const cannotApplyRoleCondition = {
    type: 'cannotApplyRoleCondition',
    conditionCheck: (context) => {
        const { rootState: state, entityWindow = {} } = context;
        const { entityId } = entityWindow;

        return getIsMarketplaceRoleApplyableSelector(state)(entityId);
    }
};

export const cannotWithdrawRoleCondition = {
    type: 'cannotWithdrawRoleCondition',
    conditionCheck: (context) => {
        const { rootState: state, entityWindow = {} } = context;
        const { entityId } = entityWindow;

        return getIsMarketplaceRoleWithdralableSelector(state)(entityId);
    }
};

export const getJobsDataCollections = (state, pageAlias) => {
    let dataCollections = {};

    if (pageAlias === JOBS_PAGE_ALIAS) {
        dataCollections = Object.keys(state[JOBS_PAGE_ALIAS].pagedData).map(key => state[JOBS_PAGE_ALIAS].pagedData[key]);
    } else if (pageAlias === PLANNER_PAGE_ALIAS) {
        const selectedWorkspaceSettings = getSelectedWorkspaceSettings(state[PLANNER_PAGE_ALIAS].workspaces);
        const masterRecTableName = getWorkspaceMasterRecTableName(selectedWorkspaceSettings);
        const masterRecGuid = getWorkspaceMasterRecPlannerDataGuid(selectedWorkspaceSettings);
        const subRecGuid = getWorkspaceSubRecPlannerDataGuid(selectedWorkspaceSettings);

        dataCollections = masterRecTableName === TABLE_NAMES.JOB
            ? [state[PLANNER_PAGE_ALIAS].pagedMasterRecPlannerData[masterRecGuid]]
            : [state[PLANNER_PAGE_ALIAS].subRecPlannerData[subRecGuid]];
    }

    return dataCollections;
};

export const cannotDuplicateJobCondition = {
    type: 'cannotDuplicateJobCondition',
    conditionCheck: (context) => {
        const { rootState: state, entityWindow = {} } = context;
        const { entityId } = entityWindow;
        const pageAlias = getCurrentPageAliasSelector(state);
        let selectedJobHasLongRunningOperation = false;

        if ([JOBS_PAGE_ALIAS, PLANNER_PAGE_ALIAS].includes(pageAlias) && entityId) {
            const dataCollections = getJobsDataCollections(state, pageAlias);
            const getDataWrapped = (tableName, id) => getData(dataCollections, tableName, id);
            const jobData = getDataWrapped(TABLE_NAMES.JOB, entityId) || {};
            selectedJobHasLongRunningOperation = jobData[JOB_LONG_RUNNING_OPERATION] !== null;
        }

        return selectedJobHasLongRunningOperation;
    }
};

export const cannotUseMoreInfoCondition = {
    type: 'cannotUseMoreInfoCondition',
    conditionCheck: (context) => {
        const { entityWindow = {} } = context;
        const { tableName } = entityWindow;

        return tableName === TABLE_NAMES.ROLEREQUESTGROUP;
    }
};

export const hideActionsMap = {
    [cannotAssignResourceToRolerequestCondition.type]: cannotAssignResourceToRolerequestCondition,
    [cannotUnassignResourceFromRolerequestCondition.type]: cannotUnassignResourceFromRolerequestCondition,
    [cannotEditResourceViaSuggestedTabCondition.type]: cannotEditResourceViaSuggestedTabCondition,
    [cannotEditEntityCondition.type]: cannotEditEntityCondition,
    [cannotEditRoleByNameCondition.type]: cannotEditRoleByNameCondition,
    [cannotEditRoleByCriteriaCondition.type]: cannotEditRoleByCriteriaCondition,
    [cannotRejectRequestCondition.type]: cannotRejectRequestCondition,
    [cannotManageBudgetRequestCondition.type]: cannotManageBudgetRequestCondition,
    [cannotApplyRoleCondition.type]: cannotApplyRoleCondition,
    [cannotWithdrawRoleCondition.type]: cannotWithdrawRoleCondition,
    [cannotDuplicateJobCondition.type]: cannotDuplicateJobCondition,
    [cannotUseMoreInfoCondition.type]: cannotUseMoreInfoCondition,
    [cannotEditRoleGroupCondition.type]: cannotEditRoleGroupCondition,
    [shouldRenderEditBookingSeriesCondition.type]: shouldRenderEditBookingSeriesCondition
    // [hideEntityWindowEditDividersCondition.type]: hideEntityWindowEditDividersCondition
};

export const disableConditionsMap = {
    [roleRequestHasInvalidResourceCondition.type]: roleRequestHasInvalidResourceCondition,
    [cannotSaveEntityWhenFormHasError.type]: cannotSaveEntityWhenFormHasError,
    [noRequirementsAddedCondition.type]: noRequirementsAddedCondition,
    [cannotPublishCustomCondition.type]: cannotPublishCustomCondition
};

//additional conditions hidding sections

const {
    COMMENTS_SECTION_TYPE,
    ATTACHMENTS_SECTION_TYPE,
    ROLE_GROUP_LIST_SECTION_TYPE,
    REQUIREMENTS_SECTION_TYPE,
    WORK_HISTORY_SECTION_TYPE
} = ENTITY_WINDOW_SECTION_TYPES;

export const shouldShowOverlappingBookingSectionCondition = {
    type: 'shouldShowOverlappingBookingSection',
    conditionCheck: (context) => {
        const { activetabKey, sectionContent } = context;
        const { SUGGESTIONS_KEY, RESOURCE_KEY } = DETAILS_PANE_TAB_KEYS;

        return [SUGGESTIONS_KEY, RESOURCE_KEY].includes(activetabKey) && sectionContent;
    },
    requiredProps: ['activetabKey', 'sectionContent']
};

export const shouldShowRequirementsSectionCondition = {
    type: 'shouldShowRequirementsSection',
    conditionCheck: (context) => {
        const { sectionType, includeSectionModule, entity } = context;

        return includeSectionModule && sectionType === REQUIREMENTS_SECTION_TYPE && getIsCriteriaRole(entity);
    },
    requiredProps: ['sectionType', 'entity', 'includeSectionModule']
};

export const shouldShowBudgetSectionCondition = {
    type: 'shouldShowBudgetSectionCondition',
    conditionCheck: (context) => {
        const { key, entity, includeSectionModule, moduleName, useMultipleAssignees } = context;
        const rolesEntityWindows = [
            ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM,
            ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL,
            ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL,
            ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
            ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
            ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL,
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
            ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL,
            ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL,
            ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE,
            ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL
        ];

        const isBudgetSection = includeSectionModule && key === ENTITY_WINDOW_SECTION_KEYS.BUDGET;
        // includeInModule condition should be updated when the new criteria budget section is added to other pages
        const includeInModule = !(rolesEntityWindows.includes(moduleName) && getIsCriteriaRole(entity) && useMultipleAssignees);

        return isBudgetSection && includeInModule;
    },
    requiredProps: ['key', 'entity', 'includeSectionModule', 'moduleName', 'useMultipleAssignees']
};

export const shouldCriteriaRoleShowBudgetSectionCondition = {
    type: 'shouldCriteriaRoleShowBudgetSectionCondition',
    conditionCheck: (context) => {
        const { key, entity, includeSectionModule } = context;

        return includeSectionModule && key === ENTITY_WINDOW_SECTION_KEYS.CRITERIA_BUDGET && getIsCriteriaRole(entity);
    },
    requiredProps: ['key', 'entity', 'includeSectionModule']
};

export const shouldShowAttachmentSectionCondition = {
    type: 'shouldShowAttachmentSection',
    conditionCheck: (context) => {
        const { isReadModeOnly, operation, attachmentIds, sectionType, includeSectionModule, repeatBookingData } = context;
        const {
            READ,
            EDIT,
            CONTEXTUAL_EDIT
        } = ENTITY_WINDOW_OPERATIONS;

        const renderInEditMode = (!isReadModeOnly && (operation === EDIT) && repeatBookingData?.editType === EDIT_REPEAT_BOOKING_TYPE.SELECTED_ONLY)
            || (!isReadModeOnly && (operation === EDIT) && repeatBookingData?.entity === null);
        const renderInContextualEditMode = !isReadModeOnly && (operation === CONTEXTUAL_EDIT);
        const renderInReadMode = operation === READ && attachmentIds.length;
        const renderWithReadModeFNA = isReadModeOnly && !!attachmentIds.length;

        return sectionType === ATTACHMENTS_SECTION_TYPE &&
            includeSectionModule && (
            renderInEditMode ||
                renderInContextualEditMode ||
                renderInReadMode ||
                renderWithReadModeFNA);
    },
    requiredProps: ['isReadModeOnly', 'operation', 'attachmentIds', 'sectionType', 'includeSectionModule', 'repeatBookingData']
};

export const shouldShowRoleGroupsSectionCondition = {
    type: 'shouldShowRoleGroupsSection',
    conditionCheck: (context) => {
        const { sectionType } = context;

        return sectionType === ROLE_GROUP_LIST_SECTION_TYPE;
    },
    requiredProps: ['sectionType']
};

export const shouldShowCommentsSectionCondition = {
    type: 'shouldShowCommentsSection',
    conditionCheck: (context) => {
        const { sectionType, operation, repeatBookingData, includeSectionModule, isReadModeOnly, entity } = context;
        const {
            READ,
            EDIT,
            CONTEXTUAL_EDIT
        } = ENTITY_WINDOW_OPERATIONS;
        const renderInEditMode = (!isReadModeOnly && (operation === EDIT) && repeatBookingData?.editType === EDIT_REPEAT_BOOKING_TYPE.SELECTED_ONLY)
            || (!isReadModeOnly && (operation === EDIT) && repeatBookingData?.entity === null);
        const renderInContextualEditMode = !isReadModeOnly && (operation === CONTEXTUAL_EDIT);
        const renderInReadMode = (operation === READ && !entity?.[BOOKING_SERIES_GUID]);

        return sectionType === COMMENTS_SECTION_TYPE &&
            includeSectionModule && (
            renderInEditMode ||
                renderInContextualEditMode ||
                renderInReadMode);
    },
    requiredProps: ['sectionType', 'operation', 'repeatBookingData', 'includeSectionModule', 'entity']
};

export const shouldShowSkillsSectionCondition = {
    type: 'shouldShowSkillsSection',
    conditionCheck: (context) => {
        const { skills = {} } = context;

        return Object.keys(skills).length;
    },
    requiredProps: ['skills']
};

export const shouldShowRoleListSectionCondition = {
    type: 'shouldShowRoleListSection',
    conditionCheck: (context) => {
        const { roles = {}, currentPageAlias } = context;
        const validModules = [ROLE_GROUP_LIST_PAGE, ROLE_INBOX_PAGE_ALIAS, PLANNER_PAGE_ALIAS];

        return validModules.includes(currentPageAlias) && Object.keys(roles).length;
    },
    requiredProps: ['roles', 'currentPageAlias']
};

export const shouldShowWorkHistorySectionCondition = {
    type: 'shouldShowWorkHistorySection',
    conditionCheck: (context) => {
        const { sectionType, isReadModeOnly, operation, moduleName, wrappedRecentWorkHistory, getFieldInfo } = context;
        const recentWorkHistoryData = wrappedRecentWorkHistory(moduleName);
        const hasRecentWorkHistory = isRecentWorkGridHasData(recentWorkHistoryData);

        const fieldInfo = getFieldInfo(TABLE_NAMES.JOB, JOB_END_DATE);
        const endDateVisible = !isFieldAccessHidden(fieldInfo);

        const {
            READ,
            EDIT,
            CONTEXTUAL_EDIT
        } = ENTITY_WINDOW_OPERATIONS;
        const renderInEditMode = (operation !== EDIT);
        const renderInReadMode = (operation === READ || operation === CONTEXTUAL_EDIT) || isReadModeOnly;

        return sectionType === WORK_HISTORY_SECTION_TYPE && hasRecentWorkHistory && endDateVisible && (
            renderInEditMode ||
            renderInReadMode);
    },
    requiredProps: ['sectionType', 'isReadModeOnly', 'operation', 'moduleName','wrappedRecentWorkHistory', 'getFieldInfo']
};

export const shouldShowJobReviewSectionCondition = {
    type: 'shouldShowJobReviewSection',
    requiredProps: [],
    conditionCheck: () => {
        return true; // Placeholder for future implementation
    }
};

export const disableSectionConditionsMap = {
    [shouldShowOverlappingBookingSectionCondition.type]: shouldShowOverlappingBookingSectionCondition,
    [shouldShowRequirementsSectionCondition.type]: shouldShowRequirementsSectionCondition,
    [shouldShowAttachmentSectionCondition.type]: shouldShowAttachmentSectionCondition,
    [shouldShowRoleGroupsSectionCondition.type]: shouldShowRoleGroupsSectionCondition,
    [shouldShowCommentsSectionCondition.type]: shouldShowCommentsSectionCondition,
    [shouldShowSkillsSectionCondition.type]: shouldShowSkillsSectionCondition,
    [shouldShowRoleListSectionCondition.type]: shouldShowRoleListSectionCondition,
    [shouldShowBudgetSectionCondition.type]: shouldShowBudgetSectionCondition,
    [shouldCriteriaRoleShowBudgetSectionCondition.type]: shouldCriteriaRoleShowBudgetSectionCondition,
    [shouldShowWorkHistorySectionCondition.type]: shouldShowWorkHistorySectionCondition,
    [shouldShowJobReviewSectionCondition.type]: shouldShowJobReviewSectionCondition
};

//TODO: consider deleting these as they are not used
export const shouldUseMultipleAssignees = {
    type: 'shouldUseMultipleAssignees',
    conditionCheck: (context) => {
        return (context || {}).useMultipleAssignees;
    }
};

export const shouldNotUseMultipleAssignees = {
    type: 'shouldNotUseMultipleAssignees',
    conditionCheck: (context) => {
        return !(context || {}).useMultipleAssignees;
    }
};

export const disableFieldConditionsMap = {
    [shouldUseMultipleAssignees.type]: shouldUseMultipleAssignees,
    [shouldNotUseMultipleAssignees.type]: shouldNotUseMultipleAssignees
};
