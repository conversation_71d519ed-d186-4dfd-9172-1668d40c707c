import { ENTITY_ACTION_KEYS } from './entityAccessConsts';
import { OPEN_SCENARIO, TABLE_NAMES } from './globalConsts';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_DETAILS_PAGE_TAB_KEYS, R<PERSON>E_GROUP_LIST_PAGE } from './jobsPageConsts';
import { MARKETPLACE_PAGE_ALIAS, PREVIEW_ENTITY_PAGE_ALIAS } from './marketplacePageConsts';
import { PLANNER_PAGE_ALIAS } from './plannerConsts';
import { ROLE_INBOX_PAGE_ALIAS } from './roleInboxPageConsts';

export const DETAILS_PANE_TAB_KEYS = {
    BOOKING_KEY: 'bookingTabKey',
    RESOURCE_KEY: 'resourceTabKey',
    JO<PERSON>_<PERSON>EY: 'jobTabKey',
    HOVER_KEY: 'hoveredTabKey',
    <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_KEY: 'roleGroupTabKey',
    <PERSON><PERSON><PERSON><PERSON><PERSON>EY: 'roleTabKey',
    SUG<PERSON>STIONS_KEY: 'suggestionsTabKey'
};

export const ENTITY_WINDOW_MODULES = {
    PLANNER_PAGE_MODAL: 'modal',
    PLANNER_PAGE_BATCH_MODAL: 'batchModal',
    PLANNER_PAGE_MODAL_SIMPLIFIED: 'modalSimplified',
    PLANNER_PAGE_DETAILS_PANE: 'detailsPane',
    PLANNER_PAGE_BATCH_DETAILS_PANE: 'batchDetailsPane',
    PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL: 'plannerPageAssigneesBudgetModal',
    JOBS_PAGE_MODAL: 'jobsPageModal',
    JOBS_PAGE_DETAILS_PANE: 'jobsPageDetailsPane',
    JOBS_PAGE_ROLE_GROUP_DETAILS_PANE: 'jobsPageRoleGroupDetailsPane',
    RESOURCES_PAGE_DETAILS_PANE: 'resourcesPageDetailsPane',
    ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE:'roleDetailsPageRoleGroupDetailsPane',
    JOBS_PAGE_BATCHED_DETAILS_PANE: 'jobsPageBatchDetailsPane',
    JOBS_PAGE_BATCH_MODAL: 'jobsPageBatchModal',
    ROLE_REQUEST_FORM: 'roleRequestForm',
    JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE: 'jobsPageRoleGroupListDetailsPane',
    JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE: 'jobsPageRoleGroupListBatchDetailsPane',
    JOBS_PAGE_RESOURCE_DETAILS_PANE: 'jobsPageResourceDetailsPane',
    ROLE_INBOX_PAGE_MODAL: 'roleInboxPageModal',
    ROLE_INBOX_PAGE_BATCH_MODAL: 'roleInboxPageBatchModal',
    ROLE_INBOX_PAGE_DETAILS_PANE: 'roleInboxPageDetailsPane',
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE: 'roleInboxBatchDetailsPane',
    ROLE_INBOX_PAGE_MODAL_SIMPLIFIED: 'roleInboxPageModalSimplified',
    ROLE_INBOX_ASSIGNEES_BUDGET_MODAL: 'roleInboxPageAssigneesBudgetModal',
    ROLE_DETAILS_PAGE_MOVE_TO_PROMPT: 'roleDetailsPageMoveToPrompt',
    PLANNER_PAGE_MOVE_TO_PROMPT: 'plannerMoveToPrompt',
    NOTIFICATION_PAGE_MODAL: 'notificationPageModal',
    PROFILE_PAGE_MODAL: 'profilePageModal',
    MANAGE_ROLE_TEMPLATES_MODAL: 'manageRoleTemplatesModal',
    MARKETPLACE_DETAILS_PANE: 'marketplaceDetailsPane',
    MARKETPLACE_PAGE_MODAL: 'marketplacePageModal',
    PREVIEW_ENTITY_MODAL: 'marketplaceFullScreenModal',
    CREATE_ROLE_TEMPLATE_MODAL: 'createRoleTemplateModal',
    ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL: 'roleInboxPagePublishRoleModal',
    ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL: 'roleInboxPageEditRolePublicationModal',
    ROLE_GROUP_MODAL: 'roleGroupModal',
    TABLE_VIEW_MODAL: 'tableViewModal',
    TABLE_VIEW_MODAL_SIMPLIFIED: 'tableViewModalSimplified',
    GLOBAL_CREATE_MODAL: 'globalCreateModal'
};

export const ENTITY_WINDOW_OPERATIONS = {
    READ: 'read',
    EDIT: 'edit',
    CREATE: 'create',
    CONTEXTUAL_EDIT: 'contextual edit'
};

export const ENTITY_WINDOW_ERROR_MESSAGES = {
    MANDATORY_FIELDS_NOT_COMPLETED: '##key##mandatoryFieldsNotCompleted###Mandatory fields must be completed',
    FORM_HAS_ERRORS: '##key##formHasErrors###This form has errors'
};

export const BATCH_ENTITES_WINDOWS_ERROR_MESSAGES = {
    UNABLE_SAVE_CHANGES: '##key##unableToSaveChanges###Unable to save changes. Please review highlighted errors.',
    FORM_HAS_ERRORS: '##key##formHasErrors###This form has errors'
};

export const ENTITY_WINDOW_CUSTOM_CONTROL_TYPES = {
    MULTIFIELD_RADIO_CONTROL: 'multifieldRadioControl',
    DATE_RANGE_CONTROL: 'dateRangeControl',
    READONLY_CURRENCY_CONTROL: 'readonlyCurrencyControl',
    CURRENCY_CONTROL: 'currencyControl',
    MULTI_VALUE_LINKED_FIELD_CONTROL: 'multiValueLinkedFieldControl',
    MULTIFIELD_SINGLE_ROW_CONTROL: 'multifieldSingleRowControl',
    DEPENDANT_LIST_CONTROL: 'dependantListControl',
    RESOURCE_INFO_CONTROL: 'resourceInfoControl',
    RADIO_LOOKUP_CONTROL: 'radioLookupControl',
    HEALTH_CONTROL: 'healthControl'
};

export const MAX_NUMBER_FORM_FIELDS_CHANGE = 2;

export const ENTITY_WINDOW_SECTION_KEYS = {
    BASIC_DETAILS: 'basicDetails',
    BUDGET_DETAILS: 'budgetDetails',
    PLANNING: 'planning',
    SKILLS: 'skills',
    OTHER_DETAILS: 'otherDetails',
    BUDGET: 'budget',
    CONTACT: 'contact',
    EMPLOYMENT_DETAILS: 'employmentDetails',
    REQUIRED_INFORMATION: 'requiredInformation',
    SYSTEM_INFO: 'systemInfo',
    COMMENTS: 'comments',
    ATTACHMENTS: 'attachments',
    FINANCIAL_INFORMATION: 'financialInformation',
    SCHEDULING: 'scheduling',
    CRITERIA_SCHEDULING: 'criteriaScheduling',
    ADDITIONAL_DETAILS: 'additionalDetails',
    ROLES: 'Roles',
    WORK_DETAILS: 'workDetails',
    ROLEGROUP_LIST: 'roleGroupList',
    RESOURCE_SUMMARY:'resourceSummary',
    OVERLAPPING_BOOKINGS: 'overlappingBookings',
    REQUIREMENTS: 'requirements',
    CRITERIA_BUDGET: 'criteriaBudget',
    CRITERIA_BUDGET_ASSIGNEES_TOTALS: 'criteriaBudgetAssigneesTotals',
    CRITERIA_BUDGET_ESTIMATES: 'criteriaBudgetEstimates',
    MILESTONES: 'milestones',
    PUBLISH_ROLE: 'publishRole',
    WORK_HISTORY: 'workHistory',
    PROJECT_HEALTH: 'projectHealth',
    CREATE_ROLE_GROUP: 'createRoleGroup',
    EDIT_ROLE_GROUP: 'editRoleGroup',
    CME: 'cMe',
    TIME_AND_FINANCIALS: 'timeAndFinancials',
    REVENUE: 'revenue',
    COSTS: 'costs',
    PROFIT: 'profit',
    HOURS: 'hours',
    JOB_REVIEW: 'jobReview'
};

export const ENTITY_LOOKUP_WINDOW_ALIAS = 'entity_lookup_window';

export const ENTITY_WINDOW_SECTION_TITLES = {
    BASIC_DETAILS: '##key##basicDetailsSectionTitle###Basic details',
    WORK_DETAILS: '##key##workDetailsSectionTitle###Work details',
    BUDGET: '##key##budgetSectionTitle###Budget',
    BUDGET_DETAILS: '##key##budgetDetailsSectionTitle###Budget details',
    PLANNING: '##key##planningSectionTitle###Planning',
    CONTACT: '##key##contactSectionTitle###Contact',
    EMPLOYMENT_DETAILS: '##key##emplyomentDetailsSectionTitle###Employment details',
    SKILLS: '##key##skillsSectionTitle###Skills',
    SYSTEM_INFO: '##key##systemInfoSectionTitle###System info',
    ADDITIONAL_INFO: '##key##additionalSectionTitle###Additional details',
    COMMENTS: '##key##commentsSectionTitle###Comments',
    ATTACHMENTS: '##key##attachmentsSectionTitle###Documents',
    FINANCIAL_INFORMATION: '##key##financialInformationSectionTitle###Financial information',
    SCHEDULING: '##key##schedulingSectionTitle###Scheduling',
    ADDITIONAL_DETAILS: '##key##additionalDetailsSectionTitle###Additional details', //This is just for showcase only
    ROLEGROUP_LIST: '##key##roleGroupListSectionTitle###Role groups',
    ROLES: '##key##rolesSectionTitle###Roles',
    RESOURCE_SUMMARY:'##key##resourceSummaryTitle###Summary',
    OVERLAPPING_BOOKINGS: '##key##overlappingBookingsTitle###Overlapping bookings and roles',
    REQUIREMENTS_SECTION: '##key##requirementsSectionTitle###Requirements',
    REQUIREMENTS: '##key##requirementsSectionTitle###Requirements',
    MILESTONES: '##key##milestonesSectionTitle###Milestones',
    WORK_HISTORY: '##key##workHistoryTitle###Recent work',
    PROJECT_HEALTH: '##key##projectHealthTitle###Project health',
    CME: '##key##cMeSectionTitle###C-me traits',
    TIME_AND_FINANCIALS: '##key##timeAndFinancialsSectionTitle###Time and financials',
    REVENUE: '##key##revenueSectionTitle###Revenue',
    COSTS: '##key##costsSectionTitle###Costs',
    PROFIT: '##key##profitSectionTitle###Profit',
    HOURS: '##key##hoursSectionTitle###Hours',
    JOB_REVIEW: '##key##jobReviewSectionTitle###Reviews'
};

export const ENTITY_WINDOW_SECTION_TYPES = {
    FIELDS_LIST_SECTION_TYPE: 'fieldsListSectionType',
    SKILLS_LIST_SECTION_TYPE: 'skillsListSectionType',
    COMMENTS_SECTION_TYPE: 'commentsSectionType',
    ATTACHMENTS_SECTION_TYPE: 'attachmentsSectionType',
    ROLE_GROUP_LIST_SECTION_TYPE: 'roleGroupListSectionType',
    ROLE_LIST_SECTION_TYPE: 'roleListSectionType',
    RESOURCE_SUMMARY_SECTION_TYPE: 'resourceSummarySectionType',
    OVERLAPPING_BOOKING_SECTION_TYPE: 'overlappingBookingsSectionType',
    REQUIREMENTS_SECTION_TYPE: 'requirementsSectionType',
    CRITERIA_BUDGET_SECTION_TYPE: 'criteriaBudgetSectionType',
    CRITERIA_ESTIMATE_BUDGET_SECTION_TYPE: 'criteriaEstimateBudgetSectionType',
    MILESTONES_SECTION_TYPE: 'milestonesSectionType',
    WORK_HISTORY_SECTION_TYPE: 'workHistorySectionType',
    TAB_SECTION_TYPE: 'tabSectionType',
    CME_SECTION_TYPE: 'cMeSectionType',
    TIME_AND_FINANCIALS_SECTION_TYPE: 'timeAndFinancialsSectionType',
    REVENUE_SECTION_TYPE: 'revenueSectionType',
    COSTS_SECTION_TYPE: 'costsSectionType',
    PROFIT_SECTION_TYPE: 'profitSectionType',
    HOURS_SECTION_TYPE: 'hoursSectionType',
    JOB_REVIEW_SECTION_TYPE: 'jobReviewSectionType'
};

export const CRITERIA_BUDGET_SECTIONS = [
    ENTITY_WINDOW_SECTION_TYPES.CRITERIA_BUDGET_SECTION_TYPE,
    ENTITY_WINDOW_SECTION_TYPES.CRITERIA_ESTIMATE_BUDGET_SECTION_TYPE
];

export const ENTITY_WINDOW_NON_COLLAPSIBLE_SECTIONS_MODULES = [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM];

export const ENTITY_WINDOW_NO_TITLE_SECTIONS_PER_MODULE = {
    [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: [ENTITY_WINDOW_SECTION_KEYS.REQUIRED_INFORMATION]
};

export const ENTITY_WINDOW_TAB_KEYS = {
    DETAILS: 'entityWindowDetailsTab',
    HISTORY: 'entityWindowHistoryTab',
    EDIT_ALL: 'entityWindowEditAllTab',
    ROLE_LIST: 'entityWindowRoleListTab',
    ROLE_TEMPLATES_LIST: 'entityWindowRoleTemplatesListTab'
};

export const DEFAULT_ACTIVE_EW_TAB_KEY = ENTITY_WINDOW_TAB_KEYS.DETAILS;
export const EW_HISTORY_TAB_TABLE_NAMES = [TABLE_NAMES.BOOKING, TABLE_NAMES.RESOURCE, TABLE_NAMES.ROLEREQUEST, TABLE_NAMES.JOB];

export const ENTITY_WINDOW_CLEAR_HIGHLIGHT_DELAY = 3000;

export const EDIT_ALL_CAROUSEL_ELEMENT_TYPE = 'editAllCarouselType';

export const EDIT_ALL_ENTITY_ID = 'editAllEntity';

export const EMPTY_STATE = 'emptyState';

export const PROGRESS_ROLES_CAROUSEL_ELEMENT_TYPE = 'progressRolesCarouselElement';

export const OVERLAPPING_BOOKING_CAROUSEL_ELEMENT_TYPE = 'overlappingBookingCarouselElement';
export const OVERLAPPING_ROLEREQUEST_CAROUSEL_ELEMENT_TYPE = 'overlappingRolerequestCarouselElement';

export const BILLING_TYPE_TIME_MATERIAL = 'Time & Materials';
export const BILLING_TYPE_FIXED_PRICE = 'Fixed price';

const {
    ARCHIVE,
    RESTART,
    EDIT,
    EDIT_ROLE_BY_NAME,
    EDIT_ROLE_BY_CRITERIA,
    REJECT,
    MAKE_LIVE,
    SUBMIT_REQUEST,
    DELETE,
    ASSIGN_TO_ROLE,
    UNASSIGN_FROM_ROLE,
    MANAGE_BUDGET,
    MOVE_PENDING_FTEs,
    REMOVE_PENDING_FTEs,
    MOVE_PENDING_RESOURCES,
    REMOVE_PENDING_RESOURCES,
    DIVIDER,
    PREVIEW_JOB_DETAILS,
    PREVIEW_ROLE_DETAILS,
    APPLY_TO_ROLE,
    WITHDRAW_ROLE_APPLICATION,
    PUBLISH,
    SAVE_AS_TEMPLATE,
    EDIT_ROLE_PUBLICATION,
    ROLL_FORWARD,
    DUPLICATE
} = ENTITY_ACTION_KEYS;

export const ENTITY_WINDOW_ACTION_GROUPS = {
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: [
        'moreInfo',
        {
            label: '##key##actionsDropdownLabel###Actions',
            placement: 'bottomLeft',
            dropdownKey: 'detailsPane_dropdown',
            actionItems: [
                MAKE_LIVE,
                SUBMIT_REQUEST,
                RESTART,
                REJECT,
                ARCHIVE,
                MOVE_PENDING_FTEs,
                REMOVE_PENDING_FTEs,
                MOVE_PENDING_RESOURCES,
                REMOVE_PENDING_RESOURCES,
                DIVIDER,
                ROLL_FORWARD,
                EDIT,
                MANAGE_BUDGET,
                SAVE_AS_TEMPLATE,
                DUPLICATE,
                DELETE
            ]
        },
        OPEN_SCENARIO,
        ASSIGN_TO_ROLE,
        UNASSIGN_FROM_ROLE
    ],
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: [
        'moreInfo',
        {
            label: '##key##actionsDropdownLabel###Actions',
            placement: 'bottomLeft',
            dropdownKey: 'detailsPane_dropdown',
            actionItems: [
                MAKE_LIVE,
                SUBMIT_REQUEST,
                ARCHIVE,
                RESTART,
                REJECT,
                DIVIDER,
                ROLL_FORWARD,
                EDIT,
                EDIT_ROLE_BY_NAME,
                EDIT_ROLE_BY_CRITERIA,
                DELETE
            ]
        }
    ],
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: [
        'moreInfo',
        {
            label: '##key##actionsDropdownLabel###Actions',
            placement: 'topLeft',
            dropdownKey: 'detailsPane_dropdown',
            actionItems: [
                MAKE_LIVE,
                SUBMIT_REQUEST,
                RESTART,
                REJECT,
                ARCHIVE,
                DIVIDER,
                PUBLISH,
                EDIT_ROLE_PUBLICATION,
                DIVIDER,
                MOVE_PENDING_FTEs,
                REMOVE_PENDING_FTEs,
                MOVE_PENDING_RESOURCES,
                REMOVE_PENDING_RESOURCES,
                DIVIDER,
                EDIT,
                DIVIDER,
                MANAGE_BUDGET,
                DIVIDER,
                SAVE_AS_TEMPLATE,
                DUPLICATE,
                DELETE
            ]
        },
        OPEN_SCENARIO,
        ASSIGN_TO_ROLE,
        UNASSIGN_FROM_ROLE
    ],
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: [
        'moreInfo',
        {
            label: '##key##actionsDropdownLabel###Actions',
            placement: 'topLeft',
            dropdownKey: 'detailsPane_dropdown',
            actionItems: [
                MAKE_LIVE,
                SUBMIT_REQUEST,
                RESTART,
                REJECT,
                ARCHIVE,
                MOVE_PENDING_FTEs,
                REMOVE_PENDING_FTEs,
                MOVE_PENDING_RESOURCES,
                REMOVE_PENDING_RESOURCES,
                DIVIDER,
                EDIT,
                EDIT_ROLE_BY_NAME,
                EDIT_ROLE_BY_CRITERIA,
                DELETE
            ]
        }
    ],
    [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: [
        'moreInfo',
        {
            label: '##key##actionsDropdownLabel###Actions',
            placement: 'bottomLeft',
            dropdownKey: 'detailsPane_dropdown',
            actionItems: [
                PREVIEW_JOB_DETAILS,
                PREVIEW_ROLE_DETAILS,
                APPLY_TO_ROLE,
                WITHDRAW_ROLE_APPLICATION
            ]
        }
    ],
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: [
        'moreInfo',
        {
            label: '##key##actionsDropdownLabel###Actions',
            placement: 'topLeft',
            dropdownKey: 'detailsPane_dropdown',
            actionItems: [
                EDIT,
                ROLL_FORWARD
            ]
        }
    ],
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: [
        'editAll',
        {
            label: '##key##actionsDropdownLabel###Actions',
            placement: 'topLeft',
            dropdownKey: 'detailsPane_dropdown',
            actionItems: [
                MAKE_LIVE,
                SUBMIT_REQUEST,
                DUPLICATE
            ]
        }
    ]
};

export const SINGLE_DETAILS_PANE_PAGE_MODULES_MAP = {
    [PLANNER_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
    [JOBS_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE,
    [ROLE_GROUP_DETAILS_PAGE]: {
        [ROLE_GROUP_DETAILS_PAGE_TAB_KEYS.JOB_KEY]: ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE,
        [ROLE_GROUP_DETAILS_PAGE_TAB_KEYS.ROLE_GROUP_KEY]: ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
        [ROLE_GROUP_DETAILS_PAGE_TAB_KEYS.RESOURCE_KEY]: ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE,
        [ROLE_GROUP_DETAILS_PAGE_TAB_KEYS.SUGGESTIONS_KEY]: ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE
    },
    [ROLE_GROUP_LIST_PAGE]: {
        [DETAILS_PANE_TAB_KEYS.ROLE_GROUP_KEY]: ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
        [DETAILS_PANE_TAB_KEYS.JOB_KEY]: ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE
    },
    [ROLE_INBOX_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
    [MARKETPLACE_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE,
    //We want to refresh the marketplace after apply/withdraw, thats why we do this bellow
    [PREVIEW_ENTITY_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE
};

export const BATCH_DETAILS_PANE_PAGE_MODULES_MAP = {
    [PLANNER_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
    [JOBS_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE,
    [ROLE_INBOX_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE
};

export const ASSIGNEE_BUDGET_MODAL_BY_PAGE = {
    [ROLE_INBOX_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    [PLANNER_PAGE_ALIAS]: ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL
};

export const RESPONSE_FIELDS_FILTER = ['response','insufficientPermissions', 'requestOperation'];

export const DETAILS_PANE_NO_ROLE_GROUPS_COINCIDENCE_STATE = 'noRoleGroupsCoincidenceState';

export const DETAILS_PANE_EMPTY_STATE_PROPS_BY_TAB_KEY = {
    [DETAILS_PANE_TAB_KEYS.ROLE_GROUP_KEY]:  {
        PANE_TAB_NO_COINCIDENCE_ICON: 'role-group',
        PANE_TAB_NO_COINCIDENCE_MESSAGE: '##key##noRoleGroupItemsCoincidenceContent###Multiple {roleGroupPluralLowerAlias} in selection',
        PANE_TAB_NO_COINCIDENCE_CONTENT: '##key##noRoleGroupItemsCoincidenceMessage###To view scenario details, select roles that are from the same scenario.'
    }
};