import createPagedDataModel from '../pagedData';
import { createFilterPaneModel } from '../filterPane/filterPane';
import {
    resourcesPageFilterGroupsConfig
} from '../filterPane/pagesFilterGroupsConfig';
import {
    TABLE_NAMES,
    BASE_FILTER_OPTIONS,
    FILTER_FIELD_NAMES
} from '../../constants/globalConsts';
import { resourcesPageCommandBarConfig } from '../commandBar/resourcesPageCommandBar';
import { RESOURCE_DESCRIPTION } from '../../constants/fieldConsts';

export default {
    pageState:{},
    uiOptions: {},
    tableName: TABLE_NAMES.RESOURCE,
    pagedData: {
        resource: createPagedDataModel(TABLE_NAMES.RESOURCE, TABLE_NAMES.RESOURCE, 20)
    },
    tableDatas: {},
    defaultSortOrder:
        {
            field: 'resource_description',
            order: 'Ascending'
        }
    ,
    selection: {
        fields: [],
        order: {
            orderFields: [
                {
                    field: 'resource_description',
                    order: 'Ascending'
                }
            ]
        },
        filter: {

        }
    },
    filters: {
        resource: {
            ...createFilterPaneModel(
                TABLE_NAMES.RESOURCE,
                TABLE_NAMES.RESOURCE,
                '',
                resourcesPageFilterGroupsConfig
            ),
            title: TABLE_NAMES.RESOURCE,
            baseFilter: {
                applied: false,
                selectedBaseFilter: BASE_FILTER_OPTIONS.ACTIVE
            }
        }
    },
    fieldOptions: {
        resource_guid: {
            loaded: false
        },
        resource_description: {
            loaded: false
        },
    },
    displayFields: [
        RESOURCE_DESCRIPTION,
        FILTER_FIELD_NAMES.RESOURCE_JOBTITLE,
        FILTER_FIELD_NAMES.RESOURCE_GRADE,
        FILTER_FIELD_NAMES.RESOURCE_MANAGER_NAME,
        FILTER_FIELD_NAMES.RESOURCE_DEPARTMENT,
        FILTER_FIELD_NAMES.RESOURCE_DIVISION,
        "Charge Rate"
    ],
    linkFields: ['resource_description'],
    commandBarConfig : resourcesPageCommandBarConfig,
    roleGroupCreationModal: {
        visible: false,
        entityId: null,
        entityName: null,
        surrogateId: null
    }
};
