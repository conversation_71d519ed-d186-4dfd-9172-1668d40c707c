import { PAGES } from '../pages/pages';
import { getCurrentPage } from '../history';
import { getNavigationCollapsed } from '../localStorage';
import workspaces from './workspaces';
import tableStructure from './tableStructure';
import contextMenu from './contextMenu';
import entityWindow from './entityWindow';
import subnavigation from './adminSetting/subNavigation';
import pagenames from './adminSetting/pageNames';
import adminCommandBar from './adminSetting/adminCommandBar';
import databaseTables from './adminSetting/databaseTables';
import colourSchemes from './adminSetting/colourScheme';
import persistData from './persistData';
import jobsPage from './jobsPage';
import rolegroupDetailsPage from './roleGroupDetailsPage';
import roleInboxPage from './roleInboxPage';
import marketplacePage from './marketplacePage';
import tableViewPage from './tableViewPage';
import diaries from './adminSetting/diaries';
import chargeRates from './adminSetting/chargeRates';
import chargeCode from './adminSetting/chargeCode';
import currency from './adminSetting/currency';
import workflows from './adminSetting/workflows';
import createSkillStructure from './skillStructure';
import { commandBarConfig } from './commandBar/plannerCommandBar';
import fieldProperties from './adminSetting/fieldProperties';
import entityConfiguration from './adminSetting/entityConfiguration';
import fieldLookupValues from './adminSetting/fieldLokupValues';
import colourSchemeFilters from './adminSetting/colourSchemeFilter/colourSchemeFilter';
import companyInformationFilters from './adminSetting/companyInformationFilter/companyInformationFilter';
import fieldLookupFilters from './adminSetting/fieldProperties/fieldLookupFilters';
import defaultValueFieldLookupFilters from './adminSetting/fieldProperties/fieldLookupFilters';
import requestError from './requestError';
import clipboard from './clipboard';
import manageMyPlans from './manageMyPlans';
import createTalentProfilePage from './talentProfile';
import timesheetsPage from './timesheetsPage';
import userManagement from './adminSetting/userManagement';
import entityLookupWindows from './entityLookupWindows';
import reportPage from './report';
import getPagesModel from './pages';
import internationalization from './internationalization';
import entityImport from './adminSetting/entityImport';
import hotKeysHelpWindow from './hotKeysHelpWindow';
import companyInformation from './adminSetting/companyInformation';
import securityProfiles from './adminSetting/securityProfiles';
import skillsConfiguration from './adminSetting/skillsConfiguration';
import adminSettingCommon from './adminSetting/adminSettingCommon';
import adminSettingRouter from './adminSetting/adminSettingRouter';
import updateAvatarWindow from './updateAvatarWindow';
import avatars from './avatars';
import entityStructure from './entityStructure';
import colourSchemeLegend from './colourSchemeLegend';
import toasterMessage from './toasterMessage';
import editResourceSkillsWindow from './editResourceSkillsWindow';
import conflicts from './adminSetting/conflicts';
import reportSettingsData from './adminSetting/reportSettingsData';
import attachments from './attachments';
import rolegroupListPage from './roleGroupListPage';
import pagesTooltips from './pagesTooltips';
import pagesTooltipContextualMenu from './pagesTooltipContextualMenu';
import { ROLE_GROUP_DETAILS_PAGE } from '../constants/jobsPageConsts';
import { roleGroupTabs } from '../state/detailsPane/detailsPane';
import { ROLE_GROUP_DETAILS_PAGE_TAB_KEYS } from '../constants';
import { rolesModalDialogTransition } from './roleTransitionDialog';
import notificationsPage from './notificationsPage';
import notificationService from './notificationService';
import rollForwardDialog from './rollForwardDialog';
import jobDuplicateDialog from './jobDuplicateDialog';
import repeatBookingDialog from './repeatBookingDialog';
import roleGroupDuplicateDialog from './roleGroupDuplicateDialog';
import peopleFinderDialog from './peopleFinderDialog';
import notificationsSettings from './adminSetting/notificationSettings';
import callerMeetsCriteria from './callerMeetsCriteria';
import previewEntityPage from './previewEntityPage';
import jobFilterDialog from './jobFilterDialog';
import massDuplicateJobs from './adminSetting/massDuplicateJobs';
import { PLANNER_COLLAPSE_MODES } from '../constants/plannerConsts';
import { getDetailsPaneTemplate } from './detailsPane/detailsPaneTemplate';
import { workHistory } from './workHistory';
import education from './educationSection';
import experience from './experienceSection';
import serviceAccountManagement from './adminSetting/serviceAccountManagement';
import operationsLogDialog from './operationLogDialog';
import cMeProfiling from './cMeProfiling';
import summaryPage from './summaryPage';
import featureManagement from './featureManagement';
import listPage from './listPage';
import resourcesPage from './resourcesPage';

const listDataModel = { listData: [], bookingList: [], currentEdit: null, loading: true, error: null };

const filters = {
    loading: true,
    autoComplete: {}
};

const pagedMasterRecPlannerData = {
    loading: true
};

const bookingGroups = {
    loading: true
};

const detailsPane = getDetailsPaneTemplate();

const subRecPlannerData = {
    loading: true
};

const idField = 'id';
const skillSectionLinkField = 'sectionId';
const levelsKeyField = idField;
const fieldsKeyField = idField;

const skillStructure = createSkillStructure(idField, idField, skillSectionLinkField, levelsKeyField, fieldsKeyField, [], [], []);
const talentProfilePage = createTalentProfilePage(null);

const plannerData = {
    loading: true
};

export default {
    internationalization,
    applicationUser: {
        id: null,
        claimsLoaded: false,
        permissionsLoaded: false,
        claims: [],
        permissions: [],
        surrogateId: null,
        securityProfileAccessLevelData: {}
    },
    navigation:{
        page: getCurrentPage(),
        collapsed: getNavigationCollapsed()
    },
    pages: getPagesModel(PAGES),
    pagesComponents: {},
    stub: Object.assign({}, listDataModel),
    tableStructure: Object.assign({}, tableStructure),
    entityStructure,
    pagesTooltips,
    pagesTooltipContextualMenu,
    plannerPage: {
        pageState: {},
        uiOptions: {
            pageNumber: 1
        },
        workspaces,
        filters,
        plannerData,
        pagedMasterRecPlannerData,
        subRecPlannerData,
        bookingGroups,
        roleGroups: {
            loading: true
        },
        rolerequestResourceGroups: {
            loading: true
        },
        detailsPane,
        contextMenu,
        fieldOptions: {},
        plannerTableDatas: {},
        commandBarConfig,
        manageMyPlans,
        showConflict: false,
        hidePotentialConflicts: true,
        multipleBarsSelectionEnabled: true,
        collapseMode: PLANNER_COLLAPSE_MODES.SHOW_BARS
    },
    tableViewPage,
    entityWindow,
    entityLookupWindows,
    adminSetting: {
        subnavigation,
        pagenames,
        adminCommandBar,
        databaseTables,
        diaries,
        chargeRates,
        chargeCode,
        currency,
        fieldProperties,
        entityConfiguration,
        colourSchemes,
        colourSchemeFilters,
        fieldLookupValues,
        conflicts,
        reportSettingsData,
        userManagement,
        entityImport,
        companyInformation,
        companyInformationFilters,
        fieldLookupFilters,
        defaultValueFieldLookupFilters,
        securityProfiles,
        skillsConfiguration,
        adminSettingCommon,
        adminSettingRouter,
        workflows,
        notificationsSettings,
        serviceAccountManagement,
        massDuplicateJobs
    },
    persistData,
    jobsPage: {
        ...jobsPage,
        detailsPane
    },
    rolegroupListPage,
    rolegroupDetailsPage: {
        roleGroupDetailsPane:{
            ...detailsPane,
            [ROLE_GROUP_DETAILS_PAGE]:{
                guid:ROLE_GROUP_DETAILS_PAGE,
                collapsed:false,
                selectedTabKey:ROLE_GROUP_DETAILS_PAGE_TAB_KEYS.RESOURCE_KEY,
                tabs:roleGroupTabs
            }
        },
        ...rolegroupDetailsPage
    },
    roleInboxPage,
    marketplacePage,
    timesheetsPage,
    skillStructure,
    resourceSkills: {},
    editResourceSkillsWindow,
    requestError,
    talentProfilePage,
    clipboard,
    reportPage,
    hotKeysHelpWindow,
    applicationError: null,
    updateAvatarWindow,
    avatars,
    companyInformation,
    colourSchemeLegend,
    toasterMessage,
    promptModal: {},
    audit: {},
    attachments,
    roleGroups: {},
    roleRequests: {},
    rolesModalDialogTransition,
    roleRequirementsState: {
        byId: [],
        map: {},
        skillsAutocomplete: {}
    },
    roleAssigneesState: {},
    suggestedResources: {},
    notificationsPage,
    notificationService,
    rollForwardDialog,
    jobDuplicateDialog,
    repeatBookingDialog,
    operationsLogDialog,
    roleGroupDuplicateDialog,
    peopleFinderDialog,
    jobFilterDialog,
    previewEntityPage,
    assigneesPotentialConflicts: {},
    workHistory,
    education,
    experience,
    callerMeetsCriteria,
    cMeProfiling,
    summaryPage,
    featureManagement,
    listPage,
    resourcesPage: {
        ...resourcesPage,
        detailsPane
    }
};