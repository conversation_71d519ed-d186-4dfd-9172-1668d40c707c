import { JOBS_PAGE_ACTIONS } from '../../constants/jobsPageConsts';
import HOT_KEYS_ACTION_TYPES from '../hotKeys/jobsPage/actionTypes';
import { getPrimaryHotKeyDescription } from '../../utils/hotKeys';
import { ENTITY_ACCESS_TYPES } from '../../constants/entityAccessConsts';
import { jobsPageHotKeysConfig } from '../hotKeys/jobsPage/hotKeysConfig';
import { COMMAND_BAR_MENUS_COMPONENT_TYPES, COMMAND_BAR_MENUS_SECTION_KEYS } from '../../constants/commandBarConsts';
import { FEATURE_FLAGS, FILTER_FIELD_NAMES, PAGE_VIEW_SETTINGS, TABLE_NAMES } from '../../constants/globalConsts';
import {
    COMMAND_BAR_ACTION_ELEMENT_TYPES,
    BASE_FILTER_OPTIONS,
    DEFAULT_BASE_FILTER_OPTION
} from '../../constants/globalConsts';
import { CREATE_FNAS_PER_TABLENAME } from '../../constants/tablesConsts';
import { LIST_PAGE_ACTIONS } from '../../constants/listPageConsts';
import { RESOURCES_PAGE_ACTIONS } from '../../constants/resourcesPageConsts';

const { MENU, MENU_ITEM, RADIO_GROUP_ELEMENT, DIVIDER, STATIC_MESSAGE } = COMMAND_BAR_MENUS_COMPONENT_TYPES;
const { ADD, EDIT } = COMMAND_BAR_MENUS_SECTION_KEYS;

const add = {
    label: '##key##addLabel###Add',
    type: MENU,
    closedIcon: 'down',
    openIcon: 'up',
    key: ADD,
    visible: false,
    className: 'addMenuListPage',
    items: [
        {
            label: '##key##jobLabel###Job',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.CREATE_JOB,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.JOB],
            showEllipsis: true,
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            hotKeyDescription: getPrimaryHotKeyDescription(jobsPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ADD_JOB]),
            options: {
                isEntityDependant : true,
                tableName : TABLE_NAMES.JOB,
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        {
            label: '##key##clientLabel###Client',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.CREATE_CLIENT,
            showEllipsis: true,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.CLIENT],
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            options: {
                isEntityDependant: true,
                tableName : TABLE_NAMES.CLIENT,
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        {
            label: '##key##scenarioLabel###Scenario',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.CREATE_ROLE_GROUP,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : TABLE_NAMES.ROLEREQUESTGROUP,
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        { type: DIVIDER, featureFlaged: FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE },
        {
            label: '##key##staticMessageAddJobsMenu###Resources can be added via User management',
            type: STATIC_MESSAGE,
            featureFlaged: FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE
        }
    ]
};

const edit = {
    label: '##key##editLabel###Edit',
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.BUTTON,
    buttonType: 'secondary',
    key: EDIT,
    icon: 'edit',
    allocatedWidth: 60,
    className: 'editListResourceButtonCB',
    onClickActionType: RESOURCES_PAGE_ACTIONS.EDIT_RESOURCE
};

const baseFilter = {
    closedIcon: 'down',
    openIcon: 'up',
    label: '##key##baseFilterLabel###View jobs',
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.ACTION_WITH_COMPONENT,
    componentType: RADIO_GROUP_ELEMENT,
    allocatedWidth : 170,
    icon: 'eye',
    size: 'large',
    onClickActionType: RESOURCES_PAGE_ACTIONS.TOGGLE_BASE_FILTER,
    valuePropName: 'selectedBaseFilter',
    className: 'baseFilterRadioButton',
    filterPayload: [
        {
            baseFilterOption: BASE_FILTER_OPTIONS.ACTIVE
        }
    ],
    items: [
        {
            value: DEFAULT_BASE_FILTER_OPTION,
            label: '##key##viewAllJobsLabel###All',
            className: 'resourcesPageBaseFilterRow'
        },
        {
            value: BASE_FILTER_OPTIONS.ACTIVE,
            label: '##key##activeResourcesLabel###Active',
            className: 'resourcesPageBaseFilterRow'
        },
        {
            value: BASE_FILTER_OPTIONS.INACTIVE,
            label: '##key##inActiveResourcesLabel###In Active',
            className: 'resourcesPageBaseFilterRow'
        }
    ],
    options: {
        isEntityDependant: true,
        tableName: TABLE_NAMES.RESOURCE,
        singularForm: false,
        labelTemplate: 'manageEntity'
    }
};

const filtersAction = {
    label: '##key##filtersLabel###Filters',
    actionKey: 'filters',
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.QUICK_ACTION,
    allocatedWidth: 40,
    onClickActionType: RESOURCES_PAGE_ACTIONS.TOGGLE_FILTER_PANE,
    icon: 'filter',
    size: 'large',
    showBadge: true,
    badgeClassName: 'filtersBadgeCount'
};

const pageTitleSection = {
    pageTitle: '##key##resourcesLabel###Resources', pageIcon: 'user', type: 'PageTitle', options: {
        isEntityDependant : true,
        tableName : TABLE_NAMES.RESOURCE,
        singularForm : false
    }
};

const jobsResRadioGroup = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.SWITCH_VIEW,
    allocatedWidth : 400,
    id: PAGE_VIEW_SETTINGS,
    onClickActionType: LIST_PAGE_ACTIONS.VIEW_SETTINGS,
    buttonView: true,
    buttonStyle: 'solid',
    options: [
        {
            value: TABLE_NAMES.JOB,
            label: '##key##jobsLabel###Jobs',
            options: {
                isEntityDependant: true,
                tableName: TABLE_NAMES.JOB,
                singularForm: false,
                labelTemplate : 'default',
                tooltipLabel: '##key##jobsLabel###Jobs'
            },
            checked: true,
            icon: 'job'
        },
        {
            value: TABLE_NAMES.RESOURCE,
            label: '##key##resourcesLabel###Resources',
            options: {
                isEntityDependant: true,
                tableName: TABLE_NAMES.RESOURCE,
                singularForm: false,
                labelTemplate : 'default',
                tooltipLabel: '##key##resourcesLabel###Resources'
            },
            icon: 'user'
        }
    ]
};

const plans = {
    key: 'plansSection',
    className: 'plans-dropdown jobs-workspaces-dropdown',
    overlayClassName: 'plans-dropdown-popup jobs-workspaces-dropdown-popup',
    placement: 'topRight',
    workspacesMessages: {
        defaultWorkspaceLabel: '##key##defaultWorkspaceLabel###Default workspace',
        newWorkspaceLabel: '##key##newWorkspaceLabel###New workspace',
        saveChangesToPublicLabel: '##key##saveChangesToPublicLabel###Save changes to public',
        saveAsNewWorkspaceLabel: '##key##saveAsNewWorkspaceLabel###Save as a new workspace',
        manageMyWorkspacesLabel: '##key##manageMyWorkspacesLabel###Manage my workspaces',
        privateWorkspacesLabel: '##key##privateWorkspacesLabel###Private workspaces',
        publicWorkspacesLabel: '##key##publicWorkspacesLabel###Public workspaces',
        noPublicWorkspacesCreatedLabel: '##key##noPublicWorkspacesCreatedLabel###No public workspaces have been created',
        noPrivateWorkspacesCreatedLabel: '##key##noPrivateWorkspacesCreatedLabel###No private workspaces have been created',
    }
};

export const resourcesPageCommandBarConfig = {
    pageTitleSection,
    menusSection: [add, plans],
    actionsSection: [edit, baseFilter, jobsResRadioGroup, filtersAction]
};
