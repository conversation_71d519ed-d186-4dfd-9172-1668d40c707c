import { JO<PERSON>_PAGE_ALIAS, PAGE_NAMES, PLANNER_PAGE_ALIAS } from '../constants';
import { ADMIN_SETTINGS_ALIAS } from '../constants/adminSettingConsts';
import { URL_PARAMS, LICENSE_KEYS_ADMIN_SETTINGS, NAV_MENU_POSITION_TOP, NAV_MENU_POSITION_MIDDLE } from '../constants/globalConsts';
import * as HelpPagesUrls from '../constants/helpPagesConsts';
import { NAV_MENU_POSITION_BOTTOM } from '../constants/globalConsts';
import { PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE } from '../constants/jobsPageConsts';
import { ROLE_INBOX_PAGE_ALIAS } from '../constants/roleInboxPageConsts';
import { TIMESHEETS_PAGE_ALIAS } from '../constants/timeSheetsPageConsts';
import { NOTIFICATIONS_PAGE_ALIAS } from '../constants/notificationsPageConsts';
import { OPERATION_LOG_DIALOG_ALIAS } from '../constants/operationLogDialogConsts';
import { MARKETPLACE_PAGE_ALIAS, PREVIEW_ENTITY_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../constants/tableViewPageConsts';
import { REPORTS_FNA } from '../constants/reportConsts';
import { SUMMARY_PAGE_ALIAS } from '../constants/summaryPageConsts';
import { LIST_PAGE_ALIAS } from '../constants/listPageConsts';

const { licenseTimesheetsEnabled } = LICENSE_KEYS_ADMIN_SETTINGS;

const {
    WORKSPACE_SURROGATE_ID,
    WORKSPACE_NAME,
    PAGE,
    PAGE_SIZE,
    RESOURCE_SURROGATE_ID,
    RESOURCE_NAME,
    JOB_SURROGATE_ID,
    JOB_NAME,
    ROLE_GROUP_SURROGATE_ID,
    ROLE_GROUP_NAME,
    TAB,
    ROLE_SURROGATE_ID,
    ROLE_NAME
} = URL_PARAMS;

export const PLANNER_PAGE_NAV_LINK = '/plans';
const JOBS_PAGE_NAV_LINK = '/jobs';
const PROFILE_PAGE_NAV_LINK = '/profile';
const ROLEGROUPLIST_PAGE_NAV_LINK = '/rolegrouplist';
const ROLEGROUPDETAINS_PAGE_NAV_LINK = '/rolegroupdetails';
const ROLEINBOX_PAGE_NAV_LINK = '/roleinbox'; // TODO: Update as per spec when ready
const TIMESHEET_PAGE_NAV_LINK = '/timesheets';
const NOTIFICATIONS_PAGE_NAV_LINK = '/notifications';
const MARKETPLACE_PAGE_NAV_LINK = '/rolesboard';
const PREVIEW_ROLE_PAGE_NAV_LINK = '/rolepreview';
const TABLE_VIEW_PAGE_NAV_LINK = '/tableview';
export const SUMMARY_PAGE_NAV_LINK = '/summary';
const LIST_PAGE_NAV_LINK = '/list';

export const PLANNER_PAGE = {
    internalName: PAGE_NAMES.PLANNER,
    name: PLANNER_PAGE_ALIAS,
    title: 'Plans',
    path: `${PLANNER_PAGE_NAV_LINK}/:${WORKSPACE_SURROGATE_ID}(-:${WORKSPACE_NAME})?`,
    urlParams: {
        pathParams: [
            WORKSPACE_SURROGATE_ID,
            WORKSPACE_NAME
        ],
        queryParams: []
    },
    navigationLink: PLANNER_PAGE_NAV_LINK,
    icon: 'plans-page',
    visible: false,
    functionalAccessName: 'Plans',
    hasAccess: true,
    helpPageUrl: HelpPagesUrls.PlannerPageHelpPageUrl,
    navBarPosition: NAV_MENU_POSITION_TOP
};

export const ADMIN_SETTINGS_PAGE = {
    internalName: PAGE_NAMES.ADMIN_SETTINGS,
    name: ADMIN_SETTINGS_ALIAS,
    title: 'Settings',
    path: '/admin-settings',
    urlParams: {},
    navigationLink: '/admin-settings',
    icon: 'settings',
    visible: true,
    alwaysVisible: false,
    functionalAccessName: 'Administration settings',
    hasAccess: true,
    helpPageUrl: {
        usermanagement: HelpPagesUrls.UserManagerPageHelpPageUrl,
        bookings: HelpPagesUrls.PlanningDataPagesHelpPageUrl,
        resources: HelpPagesUrls.PlanningDataPagesHelpPageUrl,
        clients: HelpPagesUrls.PlanningDataPagesHelpPageUrl,
        division: HelpPagesUrls.PlanningDataPagesHelpPageUrl,
        rolerequestgroup: HelpPagesUrls.PlanningDataPagesHelpPageUrl,
        department: HelpPagesUrls.PlanningDataPagesHelpPageUrl,
        rolerequest: HelpPagesUrls.PlanningDataPagesHelpPageUrl,
        serviceaccountmanagement: HelpPagesUrls.ServiceAccountManagementHelpPageUrl,
        importdata: HelpPagesUrls.ImportDataPageHelpPageUrl,
        massduplicatejobs: HelpPagesUrls.DuplicateDataHelpPageUrl,
        skills: HelpPagesUrls.SkillsPageHelpPageUrl,
        securityprofiles: HelpPagesUrls.SecurityProfilesPageHelpPageUrl,
        currency: HelpPagesUrls.CostAndRevenueCurrencyHelpPageUrl,
        chargetypes: HelpPagesUrls.CostAndRevenueCostHelpPageUrl,
        chargerates: HelpPagesUrls.CostAndRevenueCostHelpPageUrl,
        daytypes: HelpPagesUrls.DiaryPagesHelpPageUrl,
        workpatterns: HelpPagesUrls.DiaryPagesHelpPageUrl,
        diary: HelpPagesUrls.DiaryPagesHelpPageUrl,
        Fields: HelpPagesUrls.SysSettingsFieldsPageHelpPageUrl,
        Values: HelpPagesUrls.SysSettingsValuesPageHelpPageUrl,
        'Page names': HelpPagesUrls.SysSettingsPageNamesPageHelpPageUrl,
        'Colour Scheme': HelpPagesUrls.ColourSchemesPageHelpPageUrl,
        Conflicts: HelpPagesUrls.ConflictsPageHelpPageUrl,
        companyinformation: HelpPagesUrls.CompanyInformationPageHelpPageUrl,
        notifications: HelpPagesUrls.NotificationsHelpPageUrl,
        reporting: HelpPagesUrls.ReportingHelpPageUrl,
        workflowrolebyname: HelpPagesUrls.WorkflowsHelpPageUrl,
        workflowrolebyrequirement: HelpPagesUrls.WorkflowsHelpPageUrl,
    },
    navBarPosition: NAV_MENU_POSITION_BOTTOM
};

export const ROLEGROUPLISTDETAILSPAGE = {
    internalName: PAGE_NAMES.ROLEGROUPDETAILS,
    name: ROLE_GROUP_DETAILS_PAGE,
    title: 'Rolegroupdetails',
    path: `${ROLEGROUPDETAINS_PAGE_NAV_LINK}/:${ROLE_GROUP_SURROGATE_ID}(-:${ROLE_GROUP_NAME})?`,
    urlParams: {
        pathParams: [
            ROLE_GROUP_SURROGATE_ID,
            ROLE_GROUP_NAME
        ],
        queryParams: []
    },
    navigationLink: 'rolegroupdetails',
    icon: 'job',
    visible: false,
    functionalAccessName: 'ViewJobs', // change this probably
    hasAccess: true,
    helpPageUrl: HelpPagesUrls.JobsPageHelpPageUrl, // change this probably
    exact: true
};

export const ROLEGROUPLISTPAGE = {
    internalName: PAGE_NAMES.ROLEGROUPLIST,
    name: ROLE_GROUP_LIST_PAGE,
    title: 'Rolegrouplist',
    path: `${ROLEGROUPLIST_PAGE_NAV_LINK}/:${JOB_SURROGATE_ID}(-:${JOB_NAME})?`,
    urlParams: {
        pathParams: [
            JOB_SURROGATE_ID,
            JOB_NAME
        ],
        queryParams: []
    },
    navigationLink: 'rolegrouplist',
    icon: 'job',
    visible: false,
    functionalAccessName: 'ViewJobs', // change this probably
    hasAccess: true,
    helpPageUrl: HelpPagesUrls.JobsPageHelpPageUrl, // change this probably
    exact: true
};

export const ROLE_INBOX_PAGE = {
    internalName: PAGE_NAMES.ROLE_INBOX,
    name: ROLE_INBOX_PAGE_ALIAS,
    title: 'Roles',
    path: `${ROLEINBOX_PAGE_NAV_LINK}`,
    urlParams: {
        pathParams: [],
        queryParams: [
            PAGE,
            PAGE_SIZE
        ]
    },
    navigationLink: ROLEINBOX_PAGE_NAV_LINK,
    icon: 'role',
    visible: true,
    functionalAccessName: 'AccessRoles',
    hasAccess: true,
    helpPageUrl: HelpPagesUrls.RoleInboxPageHelpPageUrl,
    exact: true,
    navBarPosition: NAV_MENU_POSITION_TOP
};

export const JOBS_PAGE = {
    internalName: PAGE_NAMES.JOBS,
    name: JOBS_PAGE_ALIAS,
    title: 'Jobs',
    path: `${JOBS_PAGE_NAV_LINK}`,
    urlParams: {
        pathParams: [],
        queryParams: [
            PAGE,
            PAGE_SIZE
        ]
    },
    navigationLink: JOBS_PAGE_NAV_LINK,
    icon: 'job',
    visible: false,
    functionalAccessName: 'Job management',
    hasAccess: true,
    helpPageUrl: HelpPagesUrls.JobsPageHelpPageUrl,
    exact: true,
    navBarPosition: NAV_MENU_POSITION_TOP,
    subPages: [
        {
            ...ROLEGROUPLISTPAGE,
            parentLink: JOBS_PAGE_NAV_LINK
        },
        {
            ...ROLEGROUPLISTDETAILSPAGE,
            parentLink: JOBS_PAGE_NAV_LINK
        }
    ]
};

export const PROFILE_PAGE = {
    internalName: PAGE_NAMES.TALENT_PROFILE,
    name: PROFILE_PAGE_ALIAS,
    title: 'Talent Profile',
    path: `${PROFILE_PAGE_NAV_LINK}/:${RESOURCE_SURROGATE_ID}(-:${RESOURCE_NAME})?`,
    urlParams: {
        pathParams: [
            RESOURCE_SURROGATE_ID,
            RESOURCE_NAME
        ],
        queryParams: []
    },
    navigationLink: PROFILE_PAGE_NAV_LINK,
    icon: 'user',
    visible: false,
    functionalAccessName: 'Profiles',
    hasAccess: true,
    navBarPosition: NAV_MENU_POSITION_MIDDLE,
    helpPageUrl: HelpPagesUrls.TalentProfilePageHelpPageUrl
};

export const REPORT = {
    internalName: PAGE_NAMES.REPORT,
    name: 'report',
    title: 'Report',
    path: '/report',
    urlParams: {},
    navigationLink: '/report',
    icon: 'reports',
    visible: true,
    alwaysVisible: false,
    functionalAccessName: REPORTS_FNA,
    hasAccess: true,
    helpPageUrl: HelpPagesUrls.ReportsPageHelpPageUrl,
    navBarPosition: NAV_MENU_POSITION_TOP
};

export const OPERATION_LOG_PAGE = {
    internalName: PAGE_NAMES.OPERATION_LOG_PAGE,
    name: OPERATION_LOG_DIALOG_ALIAS,
    title: PAGE_NAMES.OPERATION_LOG_PAGE,
    urlParams: {},
    visible: true,
    alwaysVisible: false,
    functionalAccessName: PAGE_NAMES.OPERATION_LOG_PAGE,
    hasAccess: true
};

export const NOTIFICATIONS = {
    internalName: PAGE_NAMES.NOTIFICATIONS,
    name: NOTIFICATIONS_PAGE_ALIAS,
    title: 'Notifications',
    path: `${NOTIFICATIONS_PAGE_NAV_LINK}`,
    urlParams: {
        pathParams: [],
        queryParams: [
            TAB
        ]
    },
    navigationLink: NOTIFICATIONS_PAGE_NAV_LINK,
    icon: 'notifications',
    visible: true,
    alwaysVisible: false,
    hasAccess: true,
    navBarPosition: NAV_MENU_POSITION_MIDDLE,
    helpPageUrl: HelpPagesUrls.NotificationsPageHelpPageUrl,// TODO: As confirmed with designer will be taking it up later,
    showBadgeOnPageIcon: false
};

export const LOGOUT = {
    internalName: PAGE_NAMES.LOGOUT,
    name: 'logout',
    title: 'Log out',
    path: '/logout',
    urlParams: {},
    navigationLink: '/logout',
    icon: 'sign-out',
    visible: true,
    hasAccess: true,
    navBarPosition: NAV_MENU_POSITION_BOTTOM
};

export const TIMESHEETS_PAGE = {
    internalName: PAGE_NAMES.TIMESHEETS_PAGE,
    name: TIMESHEETS_PAGE_ALIAS,
    title: 'Timesheets',
    path: `${TIMESHEET_PAGE_NAV_LINK}`,
    urlParams: {
        pathParams: [],
        queryParams: []
    },
    navigationLink: TIMESHEET_PAGE_NAV_LINK,
    icon: 'time',
    visible: true,
    hasAccess: true,
    helpPageUrl: HelpPagesUrls.TimesheetPageHelpPageUrl,
    exact: true,
    licenseKey: licenseTimesheetsEnabled,
    navBarPosition: NAV_MENU_POSITION_TOP
};


export const PREVIEW_ROLE_PAGE = {
    internalName: PAGE_NAMES.PREVIEW_ROLE,
    name: PREVIEW_ENTITY_PAGE_ALIAS,
    title: 'PreviewRole',
    path: `${PREVIEW_ROLE_PAGE_NAV_LINK}/:${ROLE_SURROGATE_ID}(-:${ROLE_NAME})?`,
    urlParams: {
        pathParams: [
            ROLE_SURROGATE_ID,
            ROLE_NAME
        ],
        queryParams: []
    },
    navigationLink: 'rolepreview',
    icon: 'job',
    visible: false,
    functionalAccessName: 'RolesBoard',
    hasAccess: true,
    // helpPageUrl: HelpPagesUrls.JobsPageHelpPageUrl, do we need help page?
    exact: true,
    hideNavBar: true
};

export const MARKETPLACE_PAGE = {
    internalName: PAGE_NAMES.MARKETPLACE_PAGE,
    name: MARKETPLACE_PAGE_ALIAS,
    title: 'Roles board',
    path: `${MARKETPLACE_PAGE_NAV_LINK}`,
    urlParams: {
        pathParams: [],
        queryParams: [
            PAGE,
            PAGE_SIZE
        ]
    },
    subPages: [
        {
            ...PREVIEW_ROLE_PAGE,
            parentLink: MARKETPLACE_PAGE_NAV_LINK
        }
    ],
    navigationLink: MARKETPLACE_PAGE_NAV_LINK,
    icon: 'megaphone',
    visible: true,
    functionalAccessName: 'RolesBoard',
    hasAccess: true,
    exact: true,
    helpPageUrl: HelpPagesUrls.RolesBoardPageHelpPageUrl,
    navBarPosition: NAV_MENU_POSITION_TOP
};

export const TABLE_VIEW_PAGE = {
    internalName: PAGE_NAMES.TABLE_VIEW_PAGE,
    name: TABLE_VIEW_PAGE_ALIAS,
    title: 'Table View',
    path: `${TABLE_VIEW_PAGE_NAV_LINK}`,
    urlParams: {
        pathParams: [],
        queryParams: []
    },
    navigationLink: TABLE_VIEW_PAGE_NAV_LINK,
    icon: 'table',
    visible: true,
    functionalAccessName: 'TableView',
    hasAccess: true,
    exact: true,
    helpPageUrl: HelpPagesUrls.TableViewPageHelpPageUrl,
    navBarPosition: NAV_MENU_POSITION_TOP
};

export const SUMMARY_PAGE = {
    internalName: PAGE_NAMES.SUMMARY,
    name: SUMMARY_PAGE_ALIAS,
    title: PAGE_NAMES.SUMMARY,
    path: SUMMARY_PAGE_NAV_LINK,
    urlParams: {},
    navigationLink: SUMMARY_PAGE_NAV_LINK,
    icon: '',
    visible: true,
    alwaysVisible: false,
    functionalAccessName: PAGE_NAMES.SUMMARY,
    hasAccess: true,
    exact: true,
    helpPageUrl: HelpPagesUrls.SummaryPageHelpPageUrl,
};

export const LIST_PAGE = {
    internalName: PAGE_NAMES.LIST,
    name: LIST_PAGE_ALIAS,
    title: PAGE_NAMES.LIST,
    path: LIST_PAGE_NAV_LINK,
    urlParams: {
        pathParams: [],
        queryParams: [
            PAGE,
            PAGE_SIZE
        ]
    },
    navigationLink: LIST_PAGE_NAV_LINK,
    icon: 'list',
    visible: true,
    functionalAccessName: PAGE_NAMES.JOBS,
    hasAccess: true,
    exact: true,
    helpPageUrl: HelpPagesUrls.SummaryPageHelpPageUrl,
    navBarPosition: NAV_MENU_POSITION_TOP,
};

export const PAGE_ROUTES = [
    '/plans',
    '/planner',
    '/list',
    '/jobs'
];

export const ERROR_PAGES = {
    NOT_FOUND: {
        navigationLink: '/404'
    },
    ACCESS_DENIED: {
        navigationLink: '/403'
    }
};

export const PAGES = [
    SUMMARY_PAGE,
    PLANNER_PAGE,
    TABLE_VIEW_PAGE,
    JOBS_PAGE,
    LIST_PAGE,
    PROFILE_PAGE,
    REPORT,
    NOTIFICATIONS,
    ADMIN_SETTINGS_PAGE,
    LOGOUT,
    ROLEGROUPLISTPAGE,
    ROLEGROUPLISTDETAILSPAGE,
    ROLE_INBOX_PAGE,
    TIMESHEETS_PAGE,
    MARKETPLACE_PAGE,
    PREVIEW_ROLE_PAGE,
    OPERATION_LOG_PAGE,
];