import React from 'react';
import { connect } from 'react-redux';
import BasePage from './basePage';
import { PAGE_ACTIONS } from '../actions/navigateActions';
import {
  JOBS_PAGE_ALIAS,
  ROLE_GROUP_DETAILS_PAGE,
} from '../constants/jobsPageConsts';
import { LIST_PAGE_ALIAS } from '../constants/listPageConsts';
import {
  ConnectedDetailsPane,
  ConnectedRoleGroupDetailsPaneTabsContent,
} from '../connectedComponents/connectedRoleGroupDetailsPageDP';
import {
  ConnectedCreateRoleTemplateModalEntityWindow,
  ConnectedGlobalCreateModalEntityWindow,
  ConnectedJobsPageModalEntityWindow,
  ConnectedManageRoleTemplatesModalEntityWindow,
  ConnectedRoleDetailsPageMoveRoleModalEntityWindow,
  ConnectedRoleGroupEntityWindow,
  ConnectedRoleRequestFormEntityWindow,
} from '../connectedComponents/connectedEntityWindow';
import ConnectedProgressRolesWindow from '../connectedComponents/connectedRoleTransitionDialogs/connectedProgressRoles';
import ConnectedRejectRolesWindow from '../connectedComponents/connectedRoleTransitionDialogs/connectedRejectRoles';
import { ROLE_REQUEST_FORM, HOT_KEYS_HELP_WINDOW_SECTIONS } from '../constants';
import { getPageState } from '../selectors/pagesSelectors';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';
import { ConnectedUnsavedChangesPrompt } from '../connectedComponents/connectedPrompt/connectedPrompts/connectedUnsavedChangesPrompt';
import store from '../store/configureStore';
import { ConnectedSaveAsTemplateModal } from '../connectedComponents/connectedTemplateModal';
import { ConnectedRolegroupDetailsCommandBar } from '../connectedComponents/connectedCommandBar/connectedRoleGroupDetailsCommandBar';
import { ConnectedRoleGroupdetailsBreadcrumb } from '../connectedComponents/connectedRolegroupDetailsComponents';
import { ConnectedRoleGroupDuplicateDialog } from '../connectedComponents/connectedRoleGroupDuplicateDialog';

const ConnectedRolegroupDetailsLayout = React.lazy(() =>
  import('../connectedComponents/connectedRolegroupDetailsLayout').then(
    (module) => ({ default: module.ConnectedRolegroupDetailsLayout })
  )
);

const commandBar = {
  component: ConnectedRolegroupDetailsCommandBar,
  props: {},
  containerStyle: {
    background: 'white',
    display: 'flex',
    alignItems: 'center',
    padding: '0px',
  },
};

const detailsPane = {
  component: ConnectedDetailsPane,
  props: {
    layoutProps: {
      style: {
        background: 'white',
        overflow: 'hidden',
        position: 'fixed',
        right: '0',
        height: '100%',
      },
      width: '500px',
    },
    classNameVisible: 'PLANNER_CLEAR_SELECTION_IGNORE_CLASS',
    classNameHidden: 'details-pane-container-hidden',
    contentComponent: ConnectedRoleGroupDetailsPaneTabsContent,
    contentComponentProps: {},
    prefix: 'dp',
    collapsed: false,
    visible: true,
  },
};

const roleGroupDetails = {
  component: ConnectedRoleRequestFormEntityWindow,
  props: {
    layoutProps: {},
    moduleName: ROLE_REQUEST_FORM,
  },
};

const connectedComponents = (
  <React.Fragment>
    <ConnectedJobsPageModalEntityWindow />
    <ConnectedProgressRolesWindow />
    <ConnectedRejectRolesWindow />
    <ConnectedPromptModal pageAlias={ROLE_GROUP_DETAILS_PAGE} />
    <ConnectedUnsavedChangesPrompt moduleName={ROLE_REQUEST_FORM} />
    <ConnectedSaveAsTemplateModal pageAlias={ROLE_GROUP_DETAILS_PAGE} />
    <ConnectedRoleDetailsPageMoveRoleModalEntityWindow />
    <ConnectedManageRoleTemplatesModalEntityWindow />
    <ConnectedCreateRoleTemplateModalEntityWindow />
    <ConnectedGlobalCreateModalEntityWindow />
    <ConnectedRoleGroupEntityWindow />
    <ConnectedRoleGroupDuplicateDialog />
  </React.Fragment>
);

// Function to determine master page context based on current URL
const getMasterPageContext = () => {
  const currentPath = window.location.pathname;
  if (currentPath.includes('/list')) {
    return {
      masterPageAlias: LIST_PAGE_ALIAS,
      masterPageDisplayName: 'list',
    };
  }
  return {
    masterPageAlias: JOBS_PAGE_ALIAS,
    masterPageDisplayName: HOT_KEYS_HELP_WINDOW_SECTIONS.JOBS,
  };
};

const masterPageOptions = getMasterPageContext();

const breadcrumb = {
  component: ConnectedRoleGroupdetailsBreadcrumb,
  props: {
    pageAlias: ROLE_GROUP_DETAILS_PAGE,
    masterPageOptions,
    getPageState,
  },
};

const addNewEntity = false;

const pageObj = {
  commandBar,
  roleGroupDetails,
  detailsPane,
  connectedComponents,
  breadcrumb,
  addNewEntity,
};

class RolegroupDetailsPage extends BasePage {
  constructor(props) {
    super(props);
  }

  internalRender() {
    const allProps = { ...this.props, ...pageObj };

    return (
      <React.Suspense fallback={<div />}>
        <ConnectedRolegroupDetailsLayout {...allProps} />
      </React.Suspense>
    );
  }
}

const mapStateToProps = (state) => {
  const { parked } = state.rolegroupDetailsPage.tableDatas;

  return {
    parked,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    onPageMount: (params) => {
      const stateParams = getPageState(
        store.getState(),
        ROLE_GROUP_DETAILS_PAGE
      ).params;
      const addNewEntity =
        stateParams && stateParams.subPageOption
          ? stateParams.subPageOption.addNewEntity
          : false;
      const additionalProps =
        stateParams && stateParams.subPageOption
          ? stateParams.subPageOption.additionalProps
          : {};

      return dispatch(
        PAGE_ACTIONS.OPEN[ROLE_GROUP_DETAILS_PAGE](
          params.location,
          ROLE_GROUP_DETAILS_PAGE,
          addNewEntity,
          additionalProps
        )
      );
    },
    onPageUnmount: (params) =>
      dispatch(PAGE_ACTIONS.CLOSE[ROLE_GROUP_DETAILS_PAGE](params.location)),
    dispatchAction: (action) => dispatch(action),
  };
};

const ConnectedRolegroupDetailsPage = connect(
  mapStateToProps,
  mapDispatchToProps
)(RolegroupDetailsPage);

export default ConnectedRolegroupDetailsPage;
